language: cpp

matrix:
  include:
  - compiler: clang
    os: linux
    dist: bionic
    sudo: required
    group: edge
  - compiler: gcc
    os: linux
    dist: bionic
    sudo: required
    group: edge
  - compiler: clang
    os: osx
  - compiler: gcc
    os: osx

addons:
  apt:
    update: true
    packages:
    - libafflib-dev
    - libewf-dev
    - libpq-dev
    - autopoint
    - libsqlite3-dev
    - ant
    - ant-optional
    - libcppunit-dev
    - wget
    - openjdk-8-jdk
  homebrew:
    update: true
    packages:
    - ant
    - wget
    - libewf
    - gettext
    - cppunit
    - afflib
    taps: homebrew/cask-versions
    casks: adoptopenjdk8

python:
  - "2.7"

install:
  - ./travis_install_libs.sh

before_script:
  - if [ $TRAVIS_OS_NAME = linux ]; then 
        sudo update-alternatives --set java /usr/lib/jvm/java-8-openjdk-amd64/jre/bin/java;
        sudo update-alternatives --set javac /usr/lib/jvm/java-8-openjdk-amd64/bin/javac;
        export PATH=/usr/bin:$PATH; 
        unset JAVA_HOME;
    fi
  - if [ $TRAVIS_OS_NAME = "osx" ]; then
        export PATH=${PATH}:/usr/local/opt/gettext/bin;
        brew uninstall java --force;
        brew cask uninstall java --force;
    fi

script:
  - javac -version
  - ./bootstrap && ./configure --prefix=/usr && make
  - pushd bindings/java/ && ant -q dist 
  # don't run tests on osx; libtsk not present due to SIP on osx: VIK-6971
  - if test ${TRAVIS_OS_NAME} != "osx"; then
        ant -q test;
    fi
  - popd
  - pushd case-uco/java/ && ant -q && popd
  - make check && if [ -f "tests/test-suite.log" ];then cat tests/test-suite.log; fi ; if [ -f "unit_tests/base/test-suite.log" ];then cat unit_tests/base/test-suite.log; fi
  - if test ${TRAVIS_OS_NAME} = "linux"; then
        pushd release && ./release-unix.pl ci && popd;
    fi
