## Copyright/Non-Copyright Statements

The SleuthKit (tsk/) originates from the The Coronors Toolkit (TCT),
which was largely authored by <PERSON><PERSON><PERSON> in 1997-1999 under the IBM Public
License version 1.0:

```
Copyright (c) 1997,1998,1999, International Business Machines          
Corporation and others. All Rights Reserved.
```

The TCT code was adopted by TASK in 2002-2003 by <PERSON> under the Common
Public License version 1.0

```
Copyright (c) 2002-2003 Brian <PERSON>, @stake Inc.  All rights reserved
```

Who then adopted TASK code into the SleuthKit in 2003 under the existing
license:

```
Copyright (c) 2003-2005 Brian <PERSON>.  All rights reserved
Copyright (c) 2006-2011 Brian Carrier, Basis Technology.  All Rights reserved
```

## Other materials

* Makefile and Makefile.in

```
Copyright (C) 1994-2020 Free Software Foundation, Inc.

This Makefile.in is free software; the Free Software Foundation
gives unlimited permission to copy and/or distribute it,
with or without modifications, as long as this notice is preserved.
```

* bindings/java/jni/*

```
Copyright (c) 2020 Brian Carrier.  All Rights reserved
```

under Common Public License version 1.0

* bindings/java/src/org/sleuthkit/datamodel/*

```
Copyright 2011-2021 Basis Technology Corp.
```

under Apache License, Version 2.0

* case-uco/*

```
Copyright 2020 Basis Technology Corp.
```

under Apache License, Version 2.0

* debian/*

```
Copyright: 2001-2016 <PERSON> <<EMAIL>>
           2002      @stake Inc.
           2005      Naysawn Naderi <<EMAIL>>
           2006-2011 Joachim Metz <<EMAIL>>
           2006-2014 Basis Technology <<EMAIL>>
```

under Common Public License version 1.0

* tsk/auto/guid.cpp

```
Copyright (c) 2014 Graeme Hill (http://graemehill.ca)
```

under MIT license

* m4/ax_check_openssl.m4

```
Copyright (c) 2009,2010 Zmanda Inc. <http://www.zmanda.com/>
Copyright (c) 2009,2010 Dustin J. Mitchell <<EMAIL>>

Copying and distribution of this file, with or without modification, are
permitted in any medium without royalty provided the copyright notice and this
notice are preserved. This file is offered as-is, without any warranty
```

* m4/ax_jni_include_dir.m4

```
Copyright (c) 2008 Don Anderson <<EMAIL>>

Copying and distribution of this file, with or without modification, are
permitted in any medium without royalty provided the copyright notice and this
notice are preserved. This file is offered as-is, without any warranty
```

* m4/ax_cxx_compile_stdcxx.m4

```
Copyright (c) 2008 Benjamin Kosnik <<EMAIL>>
Copyright (c) 2012 Zack Weinberg <<EMAIL>>
Copyright (c) 2013 Roy Stogner <<EMAIL>>
Copyright (c) 2014, 2015 Google Inc.; contributed by Alexey Sokolov <<EMAIL>>
Copyright (c) 2015 Paul Norman <<EMAIL>>
Copyright (c) 2015 Moritz Klammler <<EMAIL>>
Copyright (c) 2016, 2018 Krzesimir Nowak <<EMAIL>>
Copyright (c) 2019 Enji Cooper <<EMAIL>>

Copying and distribution of this file, with or without modification, are
permitted in any medium without royalty provided the copyright notice and this
notice are preserved. This file is offered as-is, without any warranty
```

* m4/ax_pthread.m4

```
Copyright (c) 2008 Steven G. Johnson <<EMAIL>>
````

under GNU General Public License version 3 or later

* rejistry++/*

```
Copyright 2013-2015 Basis Technology Corp.
```

under Apache License, Version 2.0

* samples/*

```
Copyright (c) 2008-2011  Brian Carrier <carrier <at> sleuthkit <dot> org>
```

under 3-Clause BSD license

* tests/*

```
Copyright (c) 2008-2011 Brian Carrier.  All Rights reserved
```

under Common Public License version 1.0

* tsk/auto/sqlite3.c and tsk/auto/sqlite3.h

```
2001 September 15

The author disclaims copyright to this source code.  In place of
a legal notice, here is a blessing:

    May you do good and not evil.
    May you find forgiveness for yourself and forgive others.
    May you share freely, never taking more than you give.
```

* tsk/base/crc.c and tsk/base/crc.h

```
Copyright (C) Ross Williams, 1993. However, permission is
granted to make and distribute verbatim copies of this
document provided that this information block and copyright
notice is included. Also, the C code modules included
in this document are fully public domain.
```

* tsk/base/md5c.c

```
Copyright (C) 1991-2, RSA Data Security, Inc. Created 1991. All rights reserved.

License to copy and use this software is granted provided that it
is identified as the "RSA Data Security, Inc. MD5 Message-Digest
Algorithm" in all material mentioning or referencing this software
or this function.

License is also granted to make and use derivative works provided
that such works are identified as "derived from the RSA Data
Security, Inc. MD5 Message-Digest Algorithm" in all material
mentioning or referencing the derived work.

RSA Data Security, Inc. makes no representations concerning either
the merchantability of this software or the suitability of this
software for any particular purpose. It is provided "as is"
without express or implied warranty of any kind.

These notices must be retained in any copies of any part of this
documentation and/or software.
```

* tsk/base/sha1c.c

```
This version written November 2000 by David Ireland of 
DI Management Services Pty Limited <<EMAIL>>

Adapted from code in the Python Cryptography Toolkit, 
version 1.0.0 by A.M. Kuchling 1995.
```

as public domain

* tsk/base/tsk_unicode.c and tsk/base/tsk_unicode.h

```
Copyright 2001-2004 Unicode, Inc.

Unicode, Inc. hereby grants the right to freely use the information
supplied in this file in the creation of products supporting the
Unicode Standard, and to make copies of this file in any form
for internal or external distribution as long as this notice
remains attached.
```

* tsk/base/XGetopt.c

```
2002-2003 Hans Dietrich <<EMAIL>>

This software is released into the public domain.
You are free to use it in any way you like.
```

* tsk/fs/lzvn.c

```
Copyright (c) 2015-2016, Apple Inc. All rights reserved.
```

under 3-Clause BSD license

* tools/srchtools/srch_strings.c

```
Copyright 1993, 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002, 2003 Free Software Foundation, Inc.
```

under GNU General Public License version 2

* tools/fiwalk/*

```
2008-2013 Simson L. Garfinkel
```

as public domain

* tools/fiwalk/plugins/*

```
James Migletz and Simson Garfinkel
```

as public domain

* tools/fiwalk/src/base64.cpp and tools/fiwalk/src/base64.h

```
Copyright (C) 1996-1999 by Internet Software Consortium
```

Copyright by the Internet Software Consortium, with portions Copyright 1995
by IBM. Both are licensed under a liberal copyright that allows inclusion
in any program so long as the copyright notice is not removed.

