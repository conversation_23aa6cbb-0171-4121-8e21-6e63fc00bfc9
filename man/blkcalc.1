.TH BLKCALC 1 
.SH NAME
blkcalc \- Converts between unallocated disk unit numbers and regular
disk unit numbers.  
.SH SYNOPSIS
.B blkcalc [-dsu unit_addr] [-vV] [-i imgtype] [-o imgoffset] [-b dev_sector_size] [-f fstype] image [images]
.SH DESCRIPTION
.B blkcalc
creates a disk unit number mapping between two images, one normal and 
another that only contains the unallocated units of the first (the
default behavior of the 
.B blkls(1)
program).  One of the 
.B -d, -s,  
or 
.B -u 
options must be given.  If the 
.B -d
option is given, then the
.B unit_addr
value is the disk unit address in the regular image (i.e. from 
.B dd
).
If the unit is unallocated, its address in an unallocated image
is given.  If the 
.B -u
option is given, then the 
.B unit_addr
value is the disk unit address in the unallocated unit image (i.e. 
from 
.B blkls(1)
).  Its disk unit address in the original image is determined.  If the 
.B -s
option is given, then the
.B unit_addr
value is the disk unit address in the slack image (i.e. from blkls \-s).
The
.B image
is the full, original image (i.e. from dd).
.B blkcalc
was called 
.B dcalc
in TSK versions prior to 3.0.0.

.IP "-f fstype"
Identify the File System type of the image.
Use '\-f list' to list the supported file system types.
If not given, autodetection methods are used.
.IP "-i imgtype"
Identify the type of image file, such as raw.
Use '\-i list' to list the supported types.
If not given, autodetection methods are used.
.IP "-o imgoffset"
The sector offset where the file system starts in the image.  
.IP "-b dev_sector_size"
The size, in bytes, of the underlying device sectors.  If not given, the value in the image format is used (if it exists) or 512-bytes is assumed. 
.IP -v
Verbose output to STDERR.
.IP -V
Display version.
.IP "image [images]"
The disk or partition image to read, whose format is given with '\-i'.
Multiple image file names can be given if the image is split into multiple segments.
If only one image file is given, and its name is the first in a sequence (e.g., as indicated by ending in '.001'), subsequent image segments will be included automatically.

.PP
This is useful when 
keyword searching an image generated by
.B blkls.
This allows one to identify the original unit address and provides 
better documentation.

.SH EXAMPLE
# blkcalc \-u 64 images/wd0e

.SH "SEE ALSO"
.BR blkls (1),

.SH AUTHOR
Brian Carrier <carrier at sleuthkit dot org>

Send documentation updates to <doc-updates at sleuthkit dot org>
