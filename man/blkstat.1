.TH BLKSTAT 1 
.SH NAME
blkstat \- Display details of a file system data unit (i.e. block or sector)
.SH SYNOPSIS
.B blkstat [-f
.I fstype 
.B ] [-i imgtype] [-o imgoffset] [-b dev_sector_size]  [-vV] 
.I image [images] addr
.SH DESCRIPTION
.B blkstat
displays the allocation status of the given data unit.
.B blkstat
was called 
.B dstat
in TSK versions prior to 3.0.0.

.SH ARGUMENTS
.IP "-f fstype"
Specify the file system type.
Use '\-f list' to list the supported file system types.
If not given, autodetection methods are used.
.IP "-i imgtype"
Identify the type of image file, such as raw.
Use '\-i list' to list the supported types.
If not given, autodetection methods are used.
.IP "-o imgoffset"
The sector offset where the file system starts in the image.  
.IP "-b dev_sector_size"
The size, in bytes, of the underlying device sectors.  If not given, the value in the image format is used (if it exists) or 512-bytes is assumed.
.IP -v
Verbose output of debugging statements to stderr
.IP -V
Display version
.IP "image [images]"
The disk or partition image to read, whose format is given with '\-i'.
Multiple image file names can be given if the image is split into multiple segments.
If only one image file is given, and its name is the first in a sequence (e.g., as indicated by ending in '.001'), subsequent image segments will be included automatically.
.IP addr
Address to display stats on.  This is a fragment for UNIX file systems or
a sector for FAT.  

.SH AUTHOR
Brian Carrier <carrier at sleuthkit dot org>

Send documentation updates to <doc-updates at sleuthkit dot org>
