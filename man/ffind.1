.TH FFIND 1 
.SH NAME
ffind \- Finds the name of the file or directory using a given inode
.SH SYNOPSIS
.B ffind [-aduvV] [-f fstype] [-i imgtype] [-o imgoffset] [-b dev_sector_size] 
.I image [images] inode
.SH DESCRIPTION
.B ffind
finds the names of files or directories that are allocated to 
.I inode
on disk image 
.I image.
By default it only will only return the first name it finds.  With
some file systems, this will find deleted file names.

.SH ARGUMENTS
.IP -a
Find all occurrences of inode.
.IP -d
Find deleted entries only.
.IP "-f fstype"
Identify the file system type of the image.  
Use '\-f list' to list the supported file system types.
If not given, autodetection methods are used.
.IP -u
Find undeleted entries only.
.IP "-i imgtype"
Identify the type of image file, such as raw.
Use '\-i list' to list the supported types.
If not given, autodetection methods are used.
.IP "-o imgoffset"
The sector offset where the file system starts in the image.  
.IP "-b dev_sector_size"
The size, in bytes, of the underlying device sectors.  If not given, the value in the image format is used (if it exists) or 512-bytes is assumed.
.IP -v
Verbose output to stderr.
.IP -V
Display version.
.IP "image [images]"
The disk or partition image to read, whose format is given with '\-i'.
Multiple image file names can be given if the image is split into multiple segments.
If only one image file is given, and its name is the first in a sequence (e.g., as indicated by ending in '.001'), subsequent image segments will be included automatically.
.IP inode
Integer of inode to find.

.PP
This program searches all directory entries looking for the
given inode.  This is useful when an inode has been identified
from a disk unit address using 
.BR ifind(1).

.SH EXAMPLE
# ffind \-a image 212
.SH "SEE ALSO"
.BR ifind (1)
.SH AUTHOR
Brian Carrier <carrier at sleuthkit dot org>

Send documentation updates to <doc-updates at sleuthkit dot org>
