.TH SIGFIND 1 
.SH NAME
sigfind \- Find a binary signature in a file
.SH SYNOPSIS
.B sigfind [-b
.I bsize
.B ] [-o
.I offset
.B ] [-t
.I template
.B ] [-lV] [
.I hex_signature
.B ]
.I file

.SH DESCRIPTION
.B sigfind
searches through a file and looks for the hex_signature at a given offset.
This can be used to search for lost boot sectors, superblocks, and partition
tables. 

.SH ARGUMENTS
.IP "-b bsize"
Specify the block size in which to search.  The default is 512 and the
value must be a multiple of 512. 
.IP "-o offset"
Specify the offset in a block in which the signature must exist.  The default is 0. 
.IP "-t template"
Specify a template name that defines the signature value and offset.  Run with 
no options to get a list of supported templates.
.IP -l
The signature is stored in little-endian ordering and must therefore be reversed.
.IP -V
Display version
.IP [hex_signature]
The binary signature that you are searching for.  It must be given in
hexadecimal format.  This argument must exist if \-t is not used.
.IP file
Any raw data.

.SH "EXAMPLES"

sigfind \-o 510 \-l AA55 disk.dd

sigfind \-t fat disk.dd


.SH AUTHOR
Brian Carrier <carrier at sleuthkit dot org>

Send documentation updates to <doc-updates at sleuthkit dot org>
