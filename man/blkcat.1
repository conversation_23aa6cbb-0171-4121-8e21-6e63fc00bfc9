.TH BLKCAT 1 
.SH NAME
blkcat \- Display the contents of file system data unit in a disk image.
.SH SYNOPSIS
.B blkcat [-ahswvV] [-f fstype] [-u unit_size] [-i imgtype] [-o imgoffset] [-b dev_sector_size] 
.I image [images] unit_addr [num]

.SH DESCRIPTION
.B blkcat
displays 
.B num 
data units (default is one) starting at the unit address
.B unit_addr
from
.B image
to stdout in different formats (default is raw).
.B blkcat
was called 
.B dcat
in TSK versions prior to 3.0.0.

.SH ARGUMENTS
.IP -a
Display the contents in ASCII
.IP "-f fstype"
Specify image as a specific file type.  If 'swap' is given
here, the image will be displayed in pages of size 4096 bytes.  If 'raw'
is given, then 512-bytes is used as the default size.  The '\-u' flag
can change the default size.  
Use '\-f list' to list the supported file system types.
If not given, autodetection methods are used.
.IP -h  
Display the contents in hexdump 
.IP -s
Display statistics on the image (unit size, file block size,  \
and number of fragments).
.IP "-u unit_size"
Specify the size of the default data unit for raw, blkls, and swap 
images.
.IP "-i imgtype"
Identify the type of image file, such as raw.
Use '\-i list' to list the supported types.
If not given, autodetection methods are used.
.IP "-o imgoffset"
The sector offset where the file system starts in the image.  
.IP "-b dev_sector_size"
The size, in bytes, of the underlying device sectors.  If not given, the value in the image format is used (if it exists) or 512-bytes is assumed.
.IP -v
Verbose output to stderr.
.IP -V
Display version.
.IP -w  
Display the contents in an HTML table format.  
.IP "image [images]"
The disk or partition image to read, whose format is given with '\-i'.
Multiple image file names can be given if the image is split into multiple segments.
If only one image file is given, and its name is the first in a sequence (e.g., as indicated by ending in '.001'), subsequent image segments will be included automatically.
.IP unit_addr
Address of the disk unit to display.  The size of a unit on this 
file system can be determined using the \-s option.
.IP num 
Number of data units to display.

.PP
The basic functionality of 
.B blkcat
can also be achieved using
.BR dd.
To determine which inode has allocated
a given unit, the 
.BR ifind(1)
command can be used.

.SH EXAMPLES
# blkcat \-hw image 264 4

or 

# blkcat \-hw image 264 

.SH "SEE ALSO"
.BR ifind (1)

.SH AUTHOR
Brian Carrier <carrier at sleuthkit dot org>

Send documentation updates to <doc-updates at sleuthkit dot org>
