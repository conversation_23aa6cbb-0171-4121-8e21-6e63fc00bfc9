/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class org_sleuthkit_datamodel_SleuthkitJNI */

#ifndef _Included_org_sleuthkit_datamodel_SleuthkitJNI
#define _Included_org_sleuthkit_datamodel_SleuthkitJNI
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    getVersionNat
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_getVersionNat
  (JNIEnv *, jclass);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    startVerboseLoggingNat
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_startVerboseLoggingNat
  (JNIEnv *, jclass, jstring);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbOpenNat
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbOpenNat
  (JNIEnv *, jclass, jstring);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbNewNat
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbNewNat
  (JNIEnv *, jclass, jstring);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbBeginTransactionNat
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbBeginTransactionNat
  (JNIEnv *, jclass, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbCommitTransactionNat
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbCommitTransactionNat
  (JNIEnv *, jclass, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbRollbackTransactionNat
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbRollbackTransactionNat
  (JNIEnv *, jclass, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbAddEntryNat
 * Signature: (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbAddEntryNat
  (JNIEnv *, jclass, jstring, jstring, jstring, jstring, jstring, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbIsUpdateableNat
 * Signature: (I)Z
 */
JNIEXPORT jboolean JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbIsUpdateableNat
  (JNIEnv *, jclass, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbIsReindexableNat
 * Signature: (I)Z
 */
JNIEXPORT jboolean JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbIsReindexableNat
  (JNIEnv *, jclass, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbPathNat
 * Signature: (I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbPathNat
  (JNIEnv *, jclass, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbIndexPathNat
 * Signature: (I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbIndexPathNat
  (JNIEnv *, jclass, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbGetDisplayName
 * Signature: (I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbGetDisplayName
  (JNIEnv *, jclass, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbCloseAll
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbCloseAll
  (JNIEnv *, jclass);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbClose
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbClose
  (JNIEnv *, jclass, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbCreateIndexNat
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbCreateIndexNat
  (JNIEnv *, jclass, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbIndexExistsNat
 * Signature: (I)Z
 */
JNIEXPORT jboolean JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbIndexExistsNat
  (JNIEnv *, jclass, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbIsIdxOnlyNat
 * Signature: (I)Z
 */
JNIEXPORT jboolean JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbIsIdxOnlyNat
  (JNIEnv *, jclass, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbLookup
 * Signature: (Ljava/lang/String;I)Z
 */
JNIEXPORT jboolean JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbLookup
  (JNIEnv *, jclass, jstring, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    hashDbLookupVerbose
 * Signature: (Ljava/lang/String;I)Lorg/sleuthkit/datamodel/HashHitInfo;
 */
JNIEXPORT jobject JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_hashDbLookupVerbose
  (JNIEnv *, jclass, jstring, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    initAddImgNat
 * Signature: (Lorg/sleuthkit/datamodel/TskCaseDbBridge;Ljava/lang/String;ZZ)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_initAddImgNatPassword
  (JNIEnv *, jclass, jobject, jstring, jboolean, jboolean, jstring);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    initializeAddImgNat
 * Signature: (Lorg/sleuthkit/datamodel/TskCaseDbBridge;Ljava/lang/String;ZZZ)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_initializeAddImgPasswordNat
  (JNIEnv *, jclass, jobject, jstring, jboolean, jboolean, jboolean, jstring);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    initAddImgNat
 * Signature: (Lorg/sleuthkit/datamodel/TskCaseDbBridge;Ljava/lang/String;ZZ)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_initAddImgNat
  (JNIEnv *, jclass, jobject, jstring, jboolean, jboolean);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    initializeAddImgNat
 * Signature: (Lorg/sleuthkit/datamodel/TskCaseDbBridge;Ljava/lang/String;ZZZ)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_initializeAddImgNat
  (JNIEnv *, jclass, jobject, jstring, jboolean, jboolean, jboolean);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    runOpenAndAddImgNat
 * Signature: (JLjava/lang/String;[Ljava/lang/String;ILjava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_runOpenAndAddImgNat
  (JNIEnv *, jclass, jlong, jstring, jobjectArray, jint, jstring);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    runAddImgNat
 * Signature: (JLjava/lang/String;JJLjava/lang/String;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_runAddImgNat
  (JNIEnv *, jclass, jlong, jstring, jlong, jlong, jstring, jstring);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    stopAddImgNat
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_stopAddImgNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    finishAddImgNat
 * Signature: (J)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_finishAddImgNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    openImgNat
 * Signature: ([Ljava/lang/String;II)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_openImgNat
  (JNIEnv *, jclass, jobjectArray, jint, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    openVsNat
 * Signature: (JJ)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_openVsNat
  (JNIEnv *, jclass, jlong, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    openVolNat
 * Signature: (JJ)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_openVolNat
  (JNIEnv *, jclass, jlong, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    openPoolNat
 * Signature: (JJ)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_openPoolNat
  (JNIEnv *, jclass, jlong, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    getImgInfoForPoolNat
 * Signature: (JJ)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_getImgInfoForPoolNat
  (JNIEnv *, jclass, jlong, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    openFsNat
 * Signature: (JJ)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_openFsNat
  (JNIEnv *, jclass, jlong, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    openFsDecryptNat
 * Signature: (JJLjava/lang/String;)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_openFsDecryptNat
  (JNIEnv *, jclass, jlong, jlong, jstring);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    openFileNat
 * Signature: (JJII)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_openFileNat
  (JNIEnv *, jclass, jlong, jlong, jint, jint);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    readImgNat
 * Signature: (J[BJJ)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_readImgNat
  (JNIEnv *, jclass, jlong, jbyteArray, jlong, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    readVsNat
 * Signature: (J[BJJ)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_readVsNat
  (JNIEnv *, jclass, jlong, jbyteArray, jlong, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    readPoolNat
 * Signature: (J[BJJ)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_readPoolNat
  (JNIEnv *, jclass, jlong, jbyteArray, jlong, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    readVolNat
 * Signature: (J[BJJ)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_readVolNat
  (JNIEnv *, jclass, jlong, jbyteArray, jlong, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    readFsNat
 * Signature: (J[BJJ)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_readFsNat
  (JNIEnv *, jclass, jlong, jbyteArray, jlong, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    readFileNat
 * Signature: (J[BJIJ)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_readFileNat
  (JNIEnv *, jclass, jlong, jbyteArray, jlong, jint, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    saveFileMetaDataTextNat
 * Signature: (JLjava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_saveFileMetaDataTextNat
  (JNIEnv *, jclass, jlong, jstring);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    getPathsForImageNat
 * Signature: (J)[Ljava/lang/String;
 */
JNIEXPORT jobjectArray JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_getPathsForImageNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    getSizeForImageNat
 * Signature: (J)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_getSizeForImageNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    getTypeForImageNat
 * Signature: (J)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_getTypeForImageNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    getSectorSizeForImageNat
 * Signature: (J)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_getSectorSizeForImageNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    getMD5HashForImageNat
 * Signature: (J)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_getMD5HashForImageNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    getSha1HashForImageNat
 * Signature: (J)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_getSha1HashForImageNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    getCollectionDetailsForImageNat
 * Signature: (J)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_getCollectionDetailsForImageNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    closeImgNat
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_closeImgNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    closePoolNat
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_closePoolNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    closeVsNat
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_closeVsNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    closeFsNat
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_closeFsNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    closeFileNat
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_closeFileNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    findDeviceSizeNat
 * Signature: (Ljava/lang/String;)J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_findDeviceSizeNat
  (JNIEnv *, jclass, jstring);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    getCurDirNat
 * Signature: (J)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_getCurDirNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    isImageSupportedNat
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_isImageSupportedNat
  (JNIEnv *, jclass, jstring, jstring);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    isImageSupportedStringNat
 * Signature: (Ljava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_isImageSupportedStringNat
(JNIEnv*, jclass, jstring, jstring);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    getSleuthkitVersionNat
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_getSleuthkitVersionNat
  (JNIEnv *, jclass);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    finishImageWriterNat
 * Signature: (J)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_finishImageWriterNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    getFinishImageProgressNat
 * Signature: (J)I
 */
JNIEXPORT jint JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_getFinishImageProgressNat
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_sleuthkit_datamodel_SleuthkitJNI
 * Method:    cancelFinishImageNat
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_org_sleuthkit_datamodel_SleuthkitJNI_cancelFinishImageNat
  (JNIEnv *, jclass, jlong);

#ifdef __cplusplus
}
#endif
#endif
/* Header for class org_sleuthkit_datamodel_SleuthkitJNI_TSK_FS_FILE_READ_OFFSET_TYPE_ENUM */

#ifndef _Included_org_sleuthkit_datamodel_SleuthkitJNI_TSK_FS_FILE_READ_OFFSET_TYPE_ENUM
#define _Included_org_sleuthkit_datamodel_SleuthkitJNI_TSK_FS_FILE_READ_OFFSET_TYPE_ENUM
#ifdef __cplusplus
extern "C" {
#endif
#ifdef __cplusplus
}
#endif
#endif
/* Header for class org_sleuthkit_datamodel_SleuthkitJNI_CaseDbHandle */

#ifndef _Included_org_sleuthkit_datamodel_SleuthkitJNI_CaseDbHandle
#define _Included_org_sleuthkit_datamodel_SleuthkitJNI_CaseDbHandle
#ifdef __cplusplus
extern "C" {
#endif
#ifdef __cplusplus
}
#endif
#endif
/* Header for class org_sleuthkit_datamodel_SleuthkitJNI_CaseDbHandle_AddImageProcess */

#ifndef _Included_org_sleuthkit_datamodel_SleuthkitJNI_CaseDbHandle_AddImageProcess
#define _Included_org_sleuthkit_datamodel_SleuthkitJNI_CaseDbHandle_AddImageProcess
#ifdef __cplusplus
extern "C" {
#endif
#ifdef __cplusplus
}
#endif
#endif
/* Header for class org_sleuthkit_datamodel_SleuthkitJNI_HandleCache */

#ifndef _Included_org_sleuthkit_datamodel_SleuthkitJNI_HandleCache
#define _Included_org_sleuthkit_datamodel_SleuthkitJNI_HandleCache
#ifdef __cplusplus
extern "C" {
#endif
#ifdef __cplusplus
}
#endif
#endif
/* Header for class org_sleuthkit_datamodel_SleuthkitJNI_CaseHandles */

#ifndef _Included_org_sleuthkit_datamodel_SleuthkitJNI_CaseHandles
#define _Included_org_sleuthkit_datamodel_SleuthkitJNI_CaseHandles
#ifdef __cplusplus
extern "C" {
#endif
#ifdef __cplusplus
}
#endif
#endif
