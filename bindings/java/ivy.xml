<ivy-module version="2.0">
	<info organisation="org.sleuthkit" module="datamodel"/>
	<dependencies>
		<dependency org="joda-time" name="joda-time" rev="2.13.1" />
		<dependency org="com.google.guava" name="guava" rev="33.4.0-jre"/>
		<dependency org="com.google.guava" name="failureaccess" rev="1.0.2"/>
		<dependency org="org.apache.commons" name="commons-lang3" rev="3.17.0"/>
		<dependency org="commons-validator" name="commons-validator" rev="1.9.0"/>
		
		<dependency org="com.google.code.gson" name="gson" rev="2.12.1"/>
		
		<dependency org="junit" name="junit" rev="4.13.2"/>
		<dependency org="io.github.java-diff-utils" name="java-diff-utils" rev="4.12"/>

        <!-- NOTE: When SQLITE version is changed, also change the version number in
          debian/sleuthkit-java.install so that it gets packaged correctly on Linux -->
		<dependency org="org.xerial" name="sqlite-jdbc" rev="3.49.1.0"/>

		<dependency org="org.postgresql" name="postgresql" rev="42.7.5" >
			<artifact name="postgresql" type="jar" />
		</dependency>
		<dependency conf="default" org="com.mchange" name="c3p0" rev="0.10.2" />
		<dependency conf="default" org="com.mchange" name="mchange-commons-java" rev="0.3.2"/>
		
		<dependency org="com.zaxxer" name="SparseBitSet" rev="1.3" />
	</dependencies>
</ivy-module>

