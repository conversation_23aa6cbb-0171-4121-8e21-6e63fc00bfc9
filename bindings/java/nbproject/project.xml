<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://www.netbeans.org/ns/project/1">
    <type>org.netbeans.modules.ant.freeform</type>
    <configuration>
        <general-data xmlns="http://www.netbeans.org/ns/freeform-project/1">
            <name>DataModel</name>
        </general-data>
        <general-data xmlns="http://www.netbeans.org/ns/freeform-project/2">
            <!-- Do not use Project Properties customizer when editing this file manually. -->
            <name>DataModel</name>
            <properties/>
            <folders>
                <source-folder>
                    <label>DataModel</label>
                    <location>.</location>
                    <encoding>windows-1252</encoding>
                </source-folder>
                <source-folder>
                    <label>src</label>
                    <type>java</type>
                    <location>src</location>
                    <encoding>windows-1252</encoding>
                </source-folder>
                <source-folder>
                    <label>test</label>
                    <type>java</type>
                    <location>test</location>
                    <encoding>windows-1252</encoding>
                </source-folder>
            </folders>
            <ide-actions>
                <action name="build">
                    <target>dist</target>
                </action>
                <action name="clean">
                    <target>clean</target>
                </action>
                <action name="rebuild">
                    <target>clean</target>
                    <target>dist</target>
                </action>
                <action name="run.single">
                    <script>nbproject/ide-file-targets.xml</script>
                    <target>run-selected-file-in-test</target>
                    <context>
                        <property>run.class</property>
                        <folder>test</folder>
                        <pattern>\.java$</pattern>
                        <format>java-name</format>
                        <arity>
                            <one-file-only/>
                        </arity>
                    </context>
                </action>
                <action name="compile.single">
                    <script>nbproject/ide-file-targets.xml</script>
                    <target>compile-selected-files-in-test</target>
                    <context>
                        <property>files</property>
                        <folder>test</folder>
                        <pattern>\.java$</pattern>
                        <format>relative-path</format>
                        <arity>
                            <separated-files>,</separated-files>
                        </arity>
                    </context>
                </action>
                <action name="test">
                    <target>test</target>
                </action>
                <action name="javadoc">
                    <target>javadoc</target>
                </action>
            </ide-actions>
            <export>
                <type>folder</type>
                <location>build</location>
                <build-target>dist</build-target>
            </export>
            <export>
                <type>folder</type>
                <location>build</location>
                <build-target>dist</build-target>
            </export>
            <export>
                <type>folder</type>
                <location>test</location>
                <build-target>dist</build-target>
            </export>
            <view>
                <items>
                    <source-folder style="packages">
                        <label>src</label>
                        <location>src</location>
                    </source-folder>
                    <source-folder style="packages">
                        <label>test</label>
                        <location>test</location>
                    </source-folder>
                    <source-file>
                        <location>build.xml</location>
                    </source-file>
                </items>
                <context-menu>
                    <ide-action name="build"/>
                    <ide-action name="rebuild"/>
                    <ide-action name="clean"/>
                    <ide-action name="javadoc"/>
                    <ide-action name="test"/>
                </context-menu>
            </view>
            <subprojects/>
        </general-data>
        <java-data xmlns="http://www.netbeans.org/ns/freeform-project-java/4">
            <compilation-unit>
                <package-root>src</package-root>
                <classpath mode="compile">lib;lib/java-diff-utils-4.12.jar;lib/junit-4.13.2.jar;lib/postgresql-42.7.3.jar;lib/c3p0-0.9.5.5.jar;lib/mchange-commons-java-0.3.0.jar;lib/joda-time-2.4.jar;lib/commons-lang3-3.14.0.jar;lib/guava-33.1.0-jre.jar;lib/SparseBitSet-1.1.jar;lib/gson-2.10.1.jar;lib/commons-validator-1.8.0.jar</classpath>
                <built-to>build</built-to>
                <source-level>1.8</source-level>
            </compilation-unit>
            <compilation-unit>
                <package-root>test</package-root>
                <unit-tests/>
                <classpath mode="compile">build;lib/java-diff-utils-4.12.jar;lib/java-diff-utils-4.12-javadoc.jar;lib/java-diff-utils-4.12-sources.jar;lib/junit-4.12.jar</classpath>
                <built-to>build</built-to>
                <built-to>test</built-to>
                <source-level>1.8</source-level>
            </compilation-unit>
        </java-data>
        <preferences xmlns="http://www.netbeans.org/ns/auxiliary-configuration-preferences/1">
            <module name="org-netbeans-modules-html-editor-lib"/>
            <module name="org-netbeans-modules-editor-indent">
                <node name="CodeStyle">
                    <property name="usedProfile" value="project"/>
                    <node name="project">
                        <property name="spaces-per-tab" value="4"/>
                        <property name="tab-size" value="4"/>
                        <property name="indent-shift-width" value="4"/>
                        <property name="expand-tabs" value="false"/>
                        <property name="text-limit-width" value="80"/>
                        <property name="text-line-wrap" value="none"/>
                    </node>
                </node>
                <node name="text">
                    <node name="x-java">
                        <node name="CodeStyle">
                            <node name="project"/>
                        </node>
                    </node>
                </node>
            </module>
            <module name="org-netbeans-modules-projectimport-eclipse-core"/>
        </preferences>
    </configuration>
</project>
