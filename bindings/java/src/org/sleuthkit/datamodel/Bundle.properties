BlackboardArtifact.tskGenInfo.text=General Info
BlackboardArtifact.tskWebBookmark.text=Web Bookmarks
BlackboardArtifact.tskWebCookie.text=Web Cookies
BlackboardArtifact.tskWebHistory.text=Web History
BlackboardArtifact.tskWebDownload.text=Web Downloads
BlackboardArtifact.tsk.recentObject.text=Recent Documents
BlackboardArtifact.tskGpsTrackpoint.text=GPS Trackpoints
BlackboardArtifact.tskInstalledProg.text=Installed Programs
BlackboardArtifact.tskKeywordHits.text=Keyword Hits
BlackboardArtifact.tskHashsetHit.text=Hashset Hits
BlackboardArtifact.tskDeviceAttached.text=USB Device Attached
BlackboardArtifact.tskInterestingFileHit.text=Interesting Files
BlackboardArtifact.tskEmailMsg.text=E-Mail Messages
BlackboardArtifact.tskExtractedText.text=Extracted Text
BlackboardArtifact.tskWebSearchQuery.text=Web Search
BlackboardArtifact.tskMetadataExif.text=EXIF Metadata
BlackboardArtifact.tagFile.text=Tagged Files
BlackboardArtifact.tskTagArtifact.text=Tagged Results
BlackboardArtifact.tskOsInfo.text=Operating System Information
BlackboardArtifact.tskOsAccount.text=Operating System User Account
BlackboardArtifact.tskServiceAccount.text=Web Accounts
BlackboardArtifact.tskToolOutput.text=Raw Tool Output
BlackboardArtifact.tskContact.text=Contacts
BlackboardArtifact.tskMessage.text=Messages
BlackboardArtifact.tskCalllog.text=Call Logs
BlackboardArtifact.tskCalendarEntry.text=Calendar Entries
BlackboardArtifact.tskSpeedDialEntry.text=Speed Dial Entries
BlackboardArtifact.tskBluetoothPairing.text=BlueTooth Pairings
BlackboardArtifact.tskGpsBookmark.text=GPS Bookmarks
BlackboardArtifact.tskGpsLastKnownLocation.text=GPS Last Known Location
BlackboardArtifact.tskGpsSearch.text=GPS Searches
BlackboardArtifact.tskProgRun.text=Run Programs
BlackboardArtifact.tskEncryptionDetected.text=Encryption Detected
BlackboardArtifact.tskEncryptionSuspected.text=Encryption Suspected
BlackboardArtifact.tskExtMismatchDetected.text=Extension Mismatch Detected
BlackboardArtifact.tskInterestingArtifactHit.text=Interesting Results
BlackboardArtifact.tskRemoteDrive.text=Remote Drive
BlackboardArtifact.tskFaceDetected.text=Face Detected
BlackboardArtifact.tskAccount.text=Accounts
BlackboardArtifact.tskTLEvent.text=TL Events
BlackboardArtifact.tskObjectDetected.text=Object Detected
BlackboardArtifact.tskWIFINetwork.text=Wireless Networks
BlackboardArtifact.tskDeviceInfo.text=Device Info
BlackboardArtifact.tskSimAttached.text=SIM Attached
BlackboardArtifact.tskBluetoothAdapter.text=Bluetooth Adapter
BlackboardArtifact.tskWIFINetworkAdapter.text=Wireless Network Adapters
BlackboardArtifact.tskVerificationFailed.text=Verification Failure
BlackboardArtifact.tskDataSourceUsage.text=Data Source Usage
BlackboardArtifact.tskWebFormAutofill.text=Web Form Autofill
BlackboardArtifact.tskWebFormAddresses.text=Web Form Addresses
BlackboardArtifact.tskDownloadSource.text=Download Source
BlackboardArtifact.tskWebCache.text=Web Cache
BlackboardArtifact.tskClipboardContent.text=Clipboard Content
BlackboardArtifact.tskUserContentSuspected.text=User Content Suspected
BlackboardArtifact.tskMetadata.text=Metadata
BlackboardArtifact.tskTrack.text=GPS Track
BlackboardArtifact.tskWebAccountType.text=Web Account Type
BlackboardArtifact.tskScreenShots.text=Screenshots
BlackboardArtifact.tskDhcpInfo.text=DHCP Information
BlackboardArtifact.tskProgNotifications.text=Program Notifications
BlackboardArtifact.tskBackupEvent.text=Backup Events
BlackboardArtifact.tskDeletedProg.text=Deleted Programs
BlackboardArtifact.tskUserDeviceEvent.text=User Device Events
BlackboardArtifact.shortDescriptionDate.text=at {0}
BlackboardArtifact.tskAssociatedObject.text=Associated Object
BlackboardArtifact.tskWebCategorization.text=Web Categories
BlackboardArtifact.tskPreviouslySeen.text=Previously Seen
BlackboardArtifact.tskPreviouslyUnseen.text=Previously Unseen
BlackboardArtifact.tskPreviouslyNotable.text=Previously Notable
BlackboardArtifact.tskInterestingItem.text=Interesting Items
BlackboardArtifact.tskMalware.text=Malware
BlackboardArtifact.tskYaraHit.text=YARA Hit
BlackboardArtifact.tskGPSArea.text=GPS Area
BlackboardAttribute.tskAccountType.text=Account Type
BlackboardAttribute.tskUrl.text=URL
BlackboardAttribute.tskDatetime.text=Date/Time
BlackboardAttribute.tskName.text=Name
BlackboardAttribute.tskProgName.text=Program Name
BlackboardAttribute.tskValue.text=Value
BlackboardAttribute.tskFlag.text=Flag
BlackboardAttribute.tskPath.text=Path
BlackboardAttribute.tskKeyword.text=Keyword
BlackboardAttribute.tskKeywordRegexp.text=Keyword Regular Expression
BlackboardAttribute.tskKeywordPreview.text=Keyword Preview
BlackboardAttribute.tskKeywordSet.text=Keyword Set
BlackboardAttribute.tskUserName.text=Username
BlackboardAttribute.tskDomain.text=Domain
BlackboardAttribute.tskPassword.text=Password
BlackboardAttribute.tskNamePerson.text=Person Name
BlackboardAttribute.tskDeviceModel.text=Device Model
BlackboardAttribute.tskDeviceMake.text=Device Make
BlackboardAttribute.tskDeviceId.text=Device ID
BlackboardAttribute.tskEmail.text=Email
BlackboardAttribute.tskHashMd5.text=MD5 Hash
BlackboardAttribute.tskHashSha1.text=SHA1 Hash
BlackboardAttribute.tskHashSha225.text=SHA2-256 Hash
BlackboardAttribute.tskHashSha2512.text=SHA2-512 Hash
BlackboardAttribute.tskText.text=Text
BlackboardAttribute.tskTextFile.text=Text File
BlackboardAttribute.tskTextLanguage.text=Text Language
BlackboardAttribute.tskEntropy.text=Entropy
BlackboardAttribute.tskHashsetName.text=Hashset Name
BlackboardAttribute.tskInterestingFile.text=Interesting File
BlackboardAttribute.tskReferrer.text=Referrer URL
BlackboardAttribute.tskDateTimeAccessed.text=Date Accessed
BlackboardAttribute.tskIpAddress.text=IP Address
BlackboardAttribute.tskPhoneNumber.text=Phone Number
BlackboardAttribute.tskPathId.text=Path ID
BlackboardAttribute.tskSetName.text=Set Name
BlackboardAttribute.tskEncryptionDetected.text=Encryption Detected
BlackboardAttribute.tskMalwareDetected.text=Malware Detected
BlackboardAttribute.tskStegDetected.text=Steganography Detected
BlackboardAttribute.tskEmailTo.text=E-Mail To
BlackboardAttribute.tskEmailCc.text=E-Mail CC
BlackboardAttribute.tskEmailBcc.text=E-Mail BCC
BlackboardAttribute.tskEmailFrom.text=E-Mail From
BlackboardAttribute.tskEmailContentPlain.text=Message (Plaintext)
BlackboardAttribute.tskEmailContentHtml.text=Message (HTML)
BlackboardAttribute.tskEmailContentRtf.text=Message (RTF)
BlackboardAttribute.tskMsgId.text=Message ID
BlackboardAttribute.tskMsgReplyId.text=Message Reply ID
BlackboardAttribute.tskDateTimeRcvd.text=Date Received
BlackboardAttribute.tskDateTimeSent.text=Date Sent
BlackboardAttribute.tskSubject.text=Subject
BlackboardAttribute.tskTitle.text=Title
BlackboardAttribute.tskGeoLatitude.text=Latitude
BlackboardAttribute.tskGeoLongitude.text=Longitude
BlackboardAttribute.tskGeoVelocity.text=Velocity
BlackboardAttribute.tskGeoAltitude.text=Altitude
BlackboardAttribute.tskGeoBearing.text=Bearing
BlackboardAttribute.tskGeoHPrecision.text=Horizontal Precision
BlackboardAttribute.tskGeoVPrecision.text=Vertical Precision
BlackboardAttribute.tskGeoMapDatum.text=Map Datum
BlackboardAttribute.tskFileTypeSig.text=File Type (signature)
BlackboardAttribute.tskFileTypeExt.text=File Type (extension)
BlackboardAttribute.tskTaggedArtifact.text=Tagged Result
BlackboardAttribute.tskTagName.text=Tag Name
BlackboardAttribute.tskComment.text=Comment
BlackboardAttribute.tskUrlDecoded.text=Decoded URL
BlackboardAttribute.tskDateTimeCreated.text=Date Created
BlackboardAttribute.tskDateTimeModified.text=Date Modified
BlackboardAttribute.tskProcessorArchitecture.text=Processor Architecture
BlackboardAttribute.tskVersion.text=Version
BlackboardAttribute.tskUserId.text=User ID
BlackboardAttribute.tskDescription.text=Description
BlackboardAttribute.tskMessageType.text=Message Type
BlackboardAttribute.tskPhoneNumberHome.text=Phone Number (Home)
BlackboardAttribute.tskPhoneNumberOffice.text=Phone Number (Office)
BlackboardAttribute.tskPhoneNumberMobile.text=Phone Number (Mobile)
BlackboardAttribute.tskPhoneNumberFrom.text=From Phone Number
BlackboardAttribute.tskPhoneNumberTo.text=To Phone Number
BlackboardAttribute.tskDirection.text=Direction
BlackboardAttribute.tskEmailHome.text=Email (Home)
BlackboardAttribute.tskEmailOffice.text=Email (Office)
BlackboardAttribute.tskDateTimeStart.text=Start Date/Time
BlackboardAttribute.tskDateTimeEnd.text=End Date/Time
BlackboardAttribute.tskCalendarEntryType.text=Calendar Entry Type
BlackboardAttribute.tskLocation.text=Location
BlackboardAttribute.tskShortcut.text=Short Cut
BlackboardAttribute.tskDeviceName.text=Device Name
BlackboardAttribute.tskCategory.text=Category
BlackboardAttribute.tskEmailReplyTo.text=ReplyTo Address
BlackboardAttribute.tskServerName.text=Server Name
BlackboardAttribute.tskCount.text=Count
BlackboardAttribute.tskMinCount.text=Minimum Count
BlackboardAttribute.tskPathSource.text=Path Source
BlackboardAttribute.tskPermissions.text=Permissions
BlackboardAttribute.tskAssociatedArtifact.text=Associated Artifact
BlackboardAttribute.tskIsDeleted.text=Is Deleted
BlackboardAttribute.tskLocalPath.text=Local Path
BlackboardAttribute.tskRemotePath.text=Remote Path
BlackboardAttribute.tskProcessorName.text=Processor Name
BlackboardAttribute.tskTempDir.text=Temporary Files Directory
BlackboardAttribute.tskProductId.text=Product ID
BlackboardAttribute.tskOwner.text=Owner
BlackboardAttribute.tskOrganization.text=Organization
BlackboardAttribute.tskCardNumber.text=Card Number
BlackboardAttribute.tskCardExpiration.text=Card Expiration (YYMM)
BlackboardAttribute.tskCardServiceCode.text=Card Service Code
BlackboardAttribute.tskCardDiscretionary.text=Card Discretionary Data
BlackboardAttribute.tskCardLRC.text=Card Longitudinal Redundancy Check
BlackboardAttribute.tskKeywordSearchDocumentID.text=Keyword Search Document ID
BlackboardAttribute.tskCardScheme.text=Card Scheme
BlackboardAttribute.tskCardType.text=Card Type
BlackboardAttribute.tskBrandName.text=Brand Name
BlackboardAttribute.tskBankName.text=Bank Name
BlackboardAttribute.tskCountry.text=Country
BlackboardAttribute.tskCity.text=City
BlackboardAttribute.tskKeywordSearchType.text=Keyword Search Type
BlackboardAttribute.tskHeaders.text=Headers
BlackboardAttribute.tskId.text=ID
BlackboardAttribute.tskTLEventType.text=Event Type
BlackboardAttribute.tskSsid.text=SSID
BlackboardAttribute.tskBssid.text=BSSID
BlackboardAttribute.tskMacAddress.text=MAC Address
BlackboardAttribute.tskImei.text=IMEI
BlackboardAttribute.tskImsi.text=IMSI
BlackboardAttribute.tskIccid.text=ICCID
BlackboardAttribute.tskthreadid.text=Thread ID
BlackboardAttribute.tskdatetimedeleted.text=Time Deleted
BlackboardAttribute.tskdatetimepwdreset.text=Password Reset Date
BlackboardAttribute.tskdatetimepwdfail.text=Password Fail Date
BlackboardAttribute.tskdisplayname.text=Display Name
BlackboardAttribute.tskpasswordsettings.text=Password Settings
BlackboardAttribute.tskaccountsettings.text=Account Settings
BlackboardAttribute.tskpasswordhint.text=Password Hint
BlackboardAttribute.tskgroups.text=Groups
BlackboardAttribute.tskattachments.text=Message Attachments
BlackboardAttribute.tskgeopath.text=List of Track Points
BlackboardAttribute.tskgeowaypoints.text=List of Waypoints
BlackboardAttribute.tskdistancetraveled.text=Distance Traveled
BlackboardAttribute.tskdistancefromhome.text=Distance from Homepoint
BlackboardAttribute.tskhashphotodna.text=PhotoDNA Hash
BlackboardAttribute.tskbytessent.text=Bytes Sent
BlackboardAttribute.tskbytesreceived.text=Bytes Received
BlackboardAttribute.tsklastprinteddatetime.text=Last Printed Date
BlackboardAttribute.tskgeoareapoints.text=List of points making up the outline of an area
BlackboardAttribute.tskrule.text = Rule
BlackboardAttribute.tskActivityType.text=Activity Type
BlackboardAttribute.tskRealm.text=Realm
BlackboardAttribute.tskHost.text=Host
BlackboardAttribute.tskHomeDir.text=Home Directory
BlackboardAttribute.tskIsAdmin.text=Is Administrator
BlackboardAttribute.tskCorrelationType.text=Correlation Type
BlackboardAttribute.tskCorrelationValue.text=Correlation Value
BlackboardAttribute.tskOtherCases.text=Other Cases
AbstractFile.readLocal.exception.msg4.text=Error reading local file\: {0}
AbstractFile.readLocal.exception.msg1.text=Error reading local file, local path is not set
AbstractFile.readLocal.exception.msg2.text=Error reading local file, it does not exist at local path\: {0}
AbstractFile.readLocal.exception.msg3.text=Error reading local file, file not readable at local path\: {0}
AbstractFile.readLocal.exception.msg5.text=Cannot read local file\: {0}
DerviedFile.derivedMethod.exception.msg1.text=Error getting derived method for file id\: {0}
FsContent.readInt.err.msg.text=Image file does not exist or is inaccessible.
Image.verifyImageSize.errStr1.text=\nPossible Incomplete Image\: Error reading volume at offset {0}
Image.verifyImageSize.errStr2.text=\nPossible Incomplete Image\: Error reading volume at offset {0}
Image.verifyImageSize.errStr3.text=\nPossible Incomplete Image\: Error reading file system at offset {0}
Image.verifyImageSize.errStr4.text=\nPossible Incomplete Image\: Error reading file system at offset {0}
SlackFile.readInt.err.msg.text=Image file does not exist or is inaccessible.
SleuthkitCase.isFileFromSource.exception.msg.text=Error, data source should be parent-less (images, file-sets), got\: {0}
SleuthkitCase.isFileFromSource.exception.msg2.text=Error, data source should be Image or VirtualDirectory, got\: {0}
SleuthkitCase.SchemaVersionMismatch=Schema version does not match
SleuthkitCase.findFiles.exception.msg1.text=Error, data source should be parent-less (images, file-sets), got\: {0}
SleuthkitCase.findFiles.exception.msg2.text=Error, data source should be Image or VirtualDirectory, got\: {0}
SleuthkitCase.findFiles.exception.msg3.text=Error finding files in the data source by name,
SleuthkitCase.findFiles3.exception.msg1.text=Error, data source should be parent-less (images, file-sets), got\: {0}
SleuthkitCase.findFiles3.exception.msg2.text=Error, data source should be Image or VirtualDirectory, got\: {0}
SleuthkitCase.findFiles3.exception.msg3.text=Error finding files in the data source by name,
SleuthkitCase.addDerivedFile.exception.msg1.text=Error creating a derived file, cannot get new id of the object, file name\: {0}
SleuthkitCase.addDerivedFile.exception.msg2.text=Error creating a derived file, file name\: {0}
SleuthkitCase.addLocalFile.exception.msg1.text=Error adding local file\: {0}, parent to add to is null
SleuthkitCase.addLocalFile.exception.msg2.text=Error creating a local file, cannot get new id of the object, file name\: {0}
SleuthkitCase.addLocalFile.exception.msg3.text=Error creating a derived file, file name\: {0}
SleuthkitCase.getLastObjectId.exception.msg.text=Error closing result set after getting last object id.
TskData.tskFsNameFlagEnum.unknown=Unknown
TskData.tskFsNameFlagEnum.allocated=Allocated
TskData.tskFsNameFlagEnum.unallocated=Unallocated
TskData.tskFsMetaFlagEnum.unknown=Unknown
TskData.tskFsMetaFlagEnum.allocated=Allocated
TskData.tskFsMetaFlagEnum.unallocated=Unallocated
TskData.tskFsMetaFlagEnum.used=Used
TskData.tskFsMetaFlagEnum.unused=Unused
TskData.tskFsMetaFlagEnum.compressed=Compressed
TskData.tskFsMetaFlagEnum.orphan=Orphan
TskData.tskFsTypeEnum.autoDetect=Auto Detect
TskData.tskFsTypeEnum.NTFSautoDetect=NTFS (Auto Detection)
TskData.tskFsTypeEnum.FATautoDetect=FAT (Auto Detection)
TskData.tskFsTypeEnum.ExtXautoDetect=ExtX (Auto Detection)
TskData.tskFsTypeEnum.SWAPautoDetect=SWAP (Auto Detection)
TskData.tskFsTypeEnum.RAWautoDetect=RAW (Auto Detection)
TskData.tskFsTypeEnum.ISO9660autoDetect=ISO9660 (Auto Detection)
TskData.tskFsTypeEnum.HFSautoDetect=HFS (Auto Detection)
TskData.tskFsTypeEnum.YAFFS2autoDetect=YAFFS2 (Auto Detection)
TskData.tskFsTypeEnum.APFSautoDetect=APFS (Auto Detection)
TskData.tskFsTypeEnum.unsupported=Unsupported File System
TskData.tskImgTypeEnum.autoDetect=Auto Detect
TskData.tskImgTypeEnum.rawSingle=Raw Single
TskData.tskImgTypeEnum.rawSplit=Raw Split
TskData.tskImgTypeEnum.unknown=Unknown
TskData.tskVSTypeEnum.autoDetect=Auto Detect
TskData.tskVSTypeEnum.fake=Fake
TskData.tskVSTypeEnum.unsupported=Unsupported
TskData.tskVSTypeEnum.exception.msg1.text=No TSK_VS_TYPE_ENUM of value\: {0}
TskData.fileKnown.unknown=unknown
TskData.fileKnown.known=known
TskData.fileKnown.knownBad=notable
TskData.fileKnown.exception.msg1.text=No FileKnown of value\: {0}
TskData.tagType.unknown=unknown
TskData.tagType.known=known
TskData.tagType.knownBad=notable
TskData.tagType.suspicious=suspicious
TskData.tagType.exception.msg1.text=No TagType of value\: {0}
TskData.encodingType.exception.msg1.text=No EncodingType of value\: {0}
TskData.collectedStatus.exception.msg1.text=No CollectedStatus of value\: {0}
TskData.keywordSearchQueryType.exception.msg1.text=No KeywordSearchQueryType of value\: {0}
TskData.tskDbFilesTypeEnum.exception.msg1.text=No TSK_FILE_TYPE_ENUM of value\: {0}
TskData.objectTypeEnum.exception.msg1.text=No ObjectType of value\: {0}
TskData.tskImgTypeEnum.exception.msg1.text=No TSK_IMG_TYPE_ENUM of value\: {0}
TskData.tskFsTypeEnum.exception.msg1.text=No TSK_FS_TYPE_ENUM of value\: {0}
TskData.tskFsAttrTypeEnum.exception.msg1.text=No TSK_FS_ATTR_TYPE_ENUM of value\: {0}
TskData.tskFsNameFlagEnum.exception.msg1.text=No TSK_FS_NAME_FLAG_ENUM of value\: {0}
TskData.tskFsMetaTypeEnum.exception.msg1.text=No TSK_FS_META_TYPE_ENUM of value\: {0}
TskData.tskFsNameTypeEnum.exception.msg1.text=No TSK_FS_NAME_TYPE_ENUM matching type\: {0}
Volume.desc.text=Unknown
Volume.read.exception.msg1.text=This volume's parent should be a VolumeSystem, but it's not.
Volume.vsFlagToString.allocated=Allocated
Volume.vsFlagToString.unallocated=Unallocated
BlackboardArtifact.tskGpsRoute.text=GPS Route
BlackboardAttribute.tskGeoLatitudeStart.text=Starting Latitude
BlackboardAttribute.tskGeoLatitudeEnd.text=Ending Latitude
BlackboardAttribute.tskGeoLongitudeStart.text=Starting Longitude
BlackboardAttribute.tskGeoLongitudeEnd.text=Ending Longitude
BlackboardAttribute.tskReadStatus.text=Read
DatabaseConnectionCheck.Everything=Invalid hostname, port number, username, password, and firewall settings.
DatabaseConnectionCheck.Port=Verify that PostgreSQL server is running, it's port number, and firewall settings.
DatabaseConnectionCheck.HostnameOrPort=Invalid hostname and/or port number.
DatabaseConnectionCheck.Authentication=Invalid username and/or password.
DatabaseConnectionCheck.Access=Invalid username and/or password.
DatabaseConnectionCheck.ServerDiskSpace=PostgreSQL server issue. Check disk space and memory availabilty on the PostgreSQL server.
DatabaseConnectionCheck.ServerRestart="PostgreSQL server issue. PostgreSQL server may need to be restarted.
DatabaseConnectionCheck.InternalServerIssue=Internal PostgreSQL issue. Database may be corrupted.
DatabaseConnectionCheck.Connection=Invalid hostname, port, username, and/or password. Check firewall settings.
DatabaseConnectionCheck.Installation=Issue with installation. JDBC driver not found.
DatabaseConnectionCheck.MissingHostname=Missing hostname.
DatabaseConnectionCheck.MissingPort=Missing port number.
DatabaseConnectionCheck.MissingUsername=Missing username.
DatabaseConnectionCheck.MissingPassword=Missing password.
IngestJobInfo.IngestJobStatusType.Started.displayName=Started
IngestJobInfo.IngestJobStatusType.Cancelled.displayName=Cancelled
IngestJobInfo.IngestJobStatusType.Completed.displayName=Completed
IngestModuleInfo.IngestModuleType.FileLevel.displayName=File Level
IngestModuleInfo.IngestModuleType.DataArtifact.displayName=Data Artifact
IngestModuleInfo.IngestModuleType.AnalysisResult.displayName=Analysis Result
IngestModuleInfo.IngestModuleType.DataSourceLevel.displayName=Data Source Level
IngestModuleInfo.IngestModuleType.Multiple.displayName=Multiple
ReviewStatus.Approved=Approved
ReviewStatus.Rejected=Rejected
ReviewStatus.Undecided=Undecided
CategoryType.DataArtifact=Data Artifact
CategoryType.AnalysisResult=Analysis Result
TimelineLevelOfDetail.low=Low
TimelineLevelOfDetail.medium=Medium
TimelineLevelOfDetail.high=High
BaseTypes.fileSystem.name=File System
BaseTypes.webActivity.name=Web Activity
BaseTypes.miscTypes.name=Other
FileSystemTypes.fileModified.name=File Modified
FileSystemTypes.fileAccessed.name=File Accessed
FileSystemTypes.fileCreated.name=File Created
FileSystemTypes.fileChanged.name=File Changed
MiscTypes.message.name=Messages
MiscTypes.GPSRoutes.name=GPS Routes
MiscTypes.GPSTrackpoint.name=GPS Trackpoint
MiscTypes.Calls.name=Call Begin
MiscTypes.CallsEnd.name=Call End
MiscTypes.Email.name=Email Sent
MiscTypes.EmailRcvd.name=Email Received
MiscTypes.recentDocuments.name=Recent Documents
MiscTypes.installedPrograms.name=Program Installed
MiscTypes.exif.name=Exif
MiscTypes.devicesAttached.name=Devices Attached
MiscTypes.LogEntry.name=Log Entry
MiscTypes.Registry.name=Registry
MiscTypes.GPSBookmark.name=GPS Bookmark
MiscTypes.GPSLastknown.name=GPS Last Known Location
MiscTypes.GPSearch.name=GPS Search
MiscTypes.GPSTrack.name=GPS Track
MiscTypes.metadataLastPrinted.name=Document Last Printed
MiscTypes.metadataLastSaved.name=Document Last Saved
MiscTypes.metadataCreated.name=Document Created
MiscTypes.programexecuted.name=Program Run
RootEventType.eventTypes.name=Event Types
WebTypes.webDownloads.name=Web Downloads
WebTypes.webCookies.name=Web Cookies Create
WebTypes.webCookiesAccessed.name=Web Cookies Accessed
WebTypes.webCookiesStart.name=Web Cookies Start
WebTypes.webCookiesEnd.name=Web Cookies End
WebTypes.webBookmarks.name=Web Bookmarks
WebTypes.webHistory.name=Web History Accessed
WebTypes.webHistoryCreated.name=Web History Created
WebTypes.webSearch.name=Web Searches
WebTypes.webFormAutoFill.name=Web Form Autofill Created
WebTypes.webFormAddress.name=Web Form Address Created
WebTypes.webFormAddressModified.name=Web Form Address Modified
WebTypes.webFormAutofillAccessed.name=Web Form Autofill Accessed
CustomTypes.other.name=Standard Artifact Event
CustomTypes.userCreated.name=Manually Created Event
CustomTypes.customArtifact.name=Custom Artifact Event
EventTypeHierarchyLevel.root=Root
EventTypeHierarchyLevel.category=Category
EventTypeHierarchyLevel.event=Event
DataSourcesFilter.displayName.text=Limit data sources to
DescriptionFilter.mode.exclude=Exclude
DescriptionFilter.mode.include=Include
hashHitsFilter.displayName.text=Must have hash hit
hideKnownFilter.displayName.text=Hide Known Files
IntersectionFilter.displayName.text=Intersection 
tagsFilter.displayName.text=Must be tagged
TextFilter.displayName.text=Must include text:
TypeFilter.displayName.text=Limit event types to
FileTypesFilter.displayName.text=Limit file types to
OsAccountStatus.Unknown.text=Unknown
OsAccountStatus.Active.text=Active
OsAccountStatus.Disabled.text=Disabled
OsAccountStatus.Deleted.text=Deleted
OsAccountStatus.NonExistent.text=Non Existent
OsAccountType.Unknown.text=Unknown
OsAccountType.Service.text=Service
OsAccountType.Interactive.text=Interactive
OsAccountInstanceType.Launched.text=Launched
OsAccountInstanceType.Accessed.text=Accessed
OsAccountInstanceType.Referenced.text=Referenced
OsAccountInstanceType.Launched.descr.text=User launched a program or had an interactive session on the host.
OsAccountInstanceType.Accessed.descr.text=User accessed resources on the host via a service or created a file on the host.
OsAccountInstanceType.Referenced.descr.text=User was referenced on the host and it is unclear if they had any access. For example, if they are mentioned in a log file.
OsAccountRealm.Known.text=Known
OsAccountRealm.Inferred.text=Inferred
OsAccountRealm.Unknown.text=Unknown
OsAccountRealm.Local.text=Local
OsAccountRealm.Domain.text=Domain
Score.Priority.Normal.displayName.text=Normal
Score.Priority.Override.displayName.text=Override
Significance.Unknown.displayName.text=Unknown
Significance.LikelyNone.displayName.text=Likely Not Notable
Significance.LikelyNotable.displayName.text=Likely Notable
Significance.None.displayName.text=Not Notable
Significance.Notable.displayName.text=Notable
TimelineEventType.BackupEventStart.txt=Backup Begin
TimelineEventType.BackupEventEnd.txt=Backup End
TimelineEventType.BackupEvent.description.start=Backup Begin
TimelineEventType.BackupEvent.description.end=Backup End
TimelineEventType.BluetoothPairingLastConnection.txt=Bluetooth Pairing Last Connection
TimelineEventType.BluetoothPairing.txt=Bluetooth Pairing
TimelineEventType.CalendarEntryStart.txt=Calendar Entry Begin
TimelineEventType.CalendarEntryEnd.txt=Calendar Entry End
TimelineEventType.DeletedProgram.txt=Program Deleted 
TimelineEventType.DeletedProgramDeleted.txt=Application Deleted
TimelineEventType.OSAccountAccessed.txt=Operating System Account Accessed
TimelineEventType.OSAccountCreated.txt=Operating System Account Created
TimelineEventType.OSAccountPwdFail.txt=Operating System Account Password Fail
TimelineEventType.OSAccountPwdReset.txt=Operating System Account Password Reset
TimelineEventType.OSInfo.txt=Operating System Information
TimelineEventType.ProgramNotification.txt=Program Notification
TimelineEventType.ScreenShot.txt=Screen Shot
TimelineEventType.UserDeviceEventStart.txt=User Activity Begin
TimelineEventType.UserDeviceEventEnd.txt=User Activity End
TimelineEventType.ServiceAccount.txt=Service Account
TimelineEventType.WIFINetwork.txt=Wifi Network
TimelineEventType.WebCache.text=Web Cache
TimelineEventType.BluetoothAdapter.txt=Bluetooth Adapter
BaseTypes.geolocation.name=Geolocation
BaseTypes.communication.name=Communication
TskData.ObjectType.IMG.name=Disk Image
TskData.ObjectType.VS.name=Volume System
TskData.ObjectType.VOL.name=Volume
TskData.ObjectType.FS.name=File System
TskData.ObjectType.AbstractFile.name=File
TskData.ObjectType.Artifact.name=Artifact
TskData.ObjectType.Report.name=Report
TskData.ObjectType.Pool.name=Pool
TskData.ObjectType.OsAccount.name=OS Account
TskData.ObjectType.HostAddress.name=Host Address
TskData.ObjectType.Unsupported.name=Unsupported

