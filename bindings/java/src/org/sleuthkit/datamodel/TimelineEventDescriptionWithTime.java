/*
 * Sleuth Kit Data Model
 *
 * Copyright 2018-2019 Basis Technology Corp.
 * Contact: carrier <at> sleuthkit <dot> org
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.sleuthkit.datamodel;

/**
 * Bundles a description of an event along with the timestamp for the event.
 * Used as an intermediate object when parsing data before it is entered into
 * the DB.
 */
final class TimelineEventDescriptionWithTime extends TimelineEventDescription {

	final private long time;

	long getTime() {
		return time;
	}

	TimelineEventDescriptionWithTime(long time, String shortDescription,
			String medDescription,
			String fullDescription) {
		super(fullDescription, medDescription, shortDescription);
		this.time = time;
	}
}
