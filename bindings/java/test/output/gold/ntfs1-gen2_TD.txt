AbstractContent [	objId 0000000001	name ntfs1-gen2.E01	checkedHasChildren false	hasChildren false	childrenCount -1	uniquePath /img_ntfs1-gen2.E01	parentId -1]	Image [			size 0	ssize 512	timezone 	type 64]	
AbstractContent [	objId 0000000002	name 	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01	getParent 1]	FileSystem [	 blockCount 1008895	blockSize 512	firstInum 0	fsType TSK_FS_TYPE_NTFS	imgOffset 0	lastInum 96	rootInum 5	]
AbstractContent [	objId 0000000003	name 	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/	getParent 2]	AbstractFile [		fileType FS	ctime 1231191948	crtime 1230763442	mtime 1231191948	atime 1231271053	attrId 6	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 48	gid 0	metaAddr 5	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 56	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000004	name $AttrDef	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$AttrDef	getParent 3]	AbstractFile [		fileType FS	ctime 1230763442	crtime 1230763442	mtime 1230763442	atime 1230763442	attrId 4	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 48	gid 0	metaAddr 4	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 2560	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$AttrDef	fileHandle 0]	File [	]	md5=d9d282989870954e69117c3fa4888929
AbstractContent [	objId 0000000005	name $BadClus	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$BadClus	getParent 3]	AbstractFile [		fileType FS	ctime 1230763442	crtime 1230763442	mtime 1230763442	atime 1230763442	attrId 2	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 8	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 0	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$BadClus	fileHandle 0]	File [	]	md5=d41d8cd98f00b204e9800998ecf8427e
AbstractContent [	objId 0000000006	name $BadClus:$Bad	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$BadClus:$Bad	getParent 3]	AbstractFile [		fileType FS	ctime 1230763442	crtime 1230763442	mtime 1230763442	atime 1230763442	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 8	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 516554240	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$BadClus:$Bad	fileHandle 0]	File [	]	md5=42218a82f996b0accdbadc6d63ed8544
AbstractContent [	objId 0000000007	name $Bitmap	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$Bitmap	getParent 3]	AbstractFile [		fileType FS	ctime 1230763442	crtime 1230763442	mtime 1230763442	atime 1230763442	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 6	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 126112	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$Bitmap	fileHandle 0]	File [	]	md5=761a1c5a91957833520c84d3f8aa8eb5
AbstractContent [	objId 0000000008	name $Boot	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$Boot	getParent 3]	AbstractFile [		fileType FS	ctime 1230763442	crtime 1230763442	mtime 1230763442	atime 1230763442	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 48	gid 0	metaAddr 7	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 8192	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$Boot	fileHandle 0]	File [	]	md5=e0c2d19532f813eb4128829dc657d474
AbstractContent [	objId 0000000009	name $Extend	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$Extend	getParent 3]	AbstractFile [		fileType FS	ctime 1230763442	crtime 1230763442	mtime 1230763442	atime 1230763442	attrId 4	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 0	gid 0	metaAddr 11	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 344	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$Extend	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000010	name .	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$Extend/.	getParent 9]	AbstractFile [		fileType FS	ctime 1230763442	crtime 1230763442	mtime 1230763442	atime 1230763442	attrId 4	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 0	gid 0	metaAddr 11	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /$Extend/	size 344	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$Extend/.	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000011	name ..	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$Extend/..	getParent 9]	AbstractFile [		fileType FS	ctime 1231191948	crtime 1230763442	mtime 1231191948	atime 1231271053	attrId 6	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 48	gid 0	metaAddr 5	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /$Extend/	size 56	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$Extend/..	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000012	name $LogFile	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$LogFile	getParent 3]	AbstractFile [		fileType FS	ctime 1230763442	crtime 1230763442	mtime 1230763442	atime 1230763442	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 2	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 4685824	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$LogFile	fileHandle 0]	File [	]	md5=5b2856170824c25c6871f664957f5674
AbstractContent [	objId 0000000013	name $MFT	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$MFT	getParent 3]	AbstractFile [		fileType FS	ctime 1230763442	crtime 1230763442	mtime 1230763442	atime 1230763442	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 0	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 98304	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$MFT	fileHandle 0]	File [	]	md5=41195a4d842f579cad5ba69c594ab59b
AbstractContent [	objId 0000000014	name $MFTMirr	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$MFTMirr	getParent 3]	AbstractFile [		fileType FS	ctime 1230763442	crtime 1230763442	mtime 1230763442	atime 1230763442	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 1	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 4096	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$MFTMirr	fileHandle 0]	File [	]	md5=8d615c0dc47b82ab20ec4afc167a0496
AbstractContent [	objId 0000000015	name $Secure:$SDS	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$Secure:$SDS	getParent 3]	AbstractFile [		fileType FS	ctime 1230763442	crtime 1230763442	mtime 1230763442	atime 1230763442	attrId 8	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 9	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 263232	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$Secure:$SDS	fileHandle 0]	File [	]	md5=c73a680c03b718ca7d5e007258facc37
AbstractContent [	objId 0000000016	name $UpCase	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$UpCase	getParent 3]	AbstractFile [		fileType FS	ctime 1230763442	crtime 1230763442	mtime 1230763442	atime 1230763442	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 10	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 131072	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$UpCase	fileHandle 0]	File [	]	md5=6fa3db2468275286210751e869d36373
AbstractContent [	objId 0000000017	name $Volume	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$Volume	getParent 3]	AbstractFile [		fileType FS	ctime 1230763442	crtime 1230763442	mtime 1230763442	atime 1230763442	attrId 3	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 48	gid 0	metaAddr 3	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 0	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$Volume	fileHandle 0]	File [	]	md5=d41d8cd98f00b204e9800998ecf8427e
AbstractContent [	objId 0000000018	name .	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/.	getParent 3]	AbstractFile [		fileType FS	ctime 1231191948	crtime 1230763442	mtime 1231191948	atime 1231271053	attrId 6	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 48	gid 0	metaAddr 5	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 56	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/.	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000019	name Compressed	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Compressed	getParent 3]	AbstractFile [		fileType FS	ctime 1231192820	crtime 1230763473	mtime 1231192820	atime 1231192820	attrId 8	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 0	gid 0	metaAddr 28	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 56	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Compressed	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000020	name .	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Compressed/.	getParent 19]	AbstractFile [		fileType FS	ctime 1231192820	crtime 1230763473	mtime 1231192820	atime 1231192820	attrId 8	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 0	gid 0	metaAddr 28	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /Compressed/	size 56	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Compressed/.	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000021	name ..	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Compressed/..	getParent 19]	AbstractFile [		fileType FS	ctime 1231191948	crtime 1230763442	mtime 1231191948	atime 1231271053	attrId 6	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 48	gid 0	metaAddr 5	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /Compressed/	size 56	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Compressed/..	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000022	name 20076517123273.pdf	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Compressed/20076517123273.pdf	getParent 19]	AbstractFile [		fileType FS	ctime 1230765336	crtime 1230765380	mtime 1230765330	atime 1230765380	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 40	metaFlags [Allocated, Used, Compressed]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /Compressed/	size 1001647	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Compressed/20076517123273.pdf	fileHandle 0]	File [	]	md5=bca818f322a5765e0c8a9c8b15cbe288
AbstractContent [	objId 0000000023	name logfile1.txt	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Compressed/logfile1.txt	getParent 19]	AbstractFile [		fileType FS	ctime 1231192885	crtime 1231192820	mtime 1231192885	atime 1231192885	attrId 6	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 48	metaFlags [Allocated, Used, Compressed]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /Compressed/	size 21888890	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Compressed/logfile1.txt	fileHandle 0]	File [	]	md5=66597ccbb89d7b030e6a2e848117e864
AbstractContent [	objId 0000000024	name NISTSP800-88_rev1.pdf	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Compressed/NISTSP800-88_rev1.pdf	getParent 19]	AbstractFile [		fileType FS	ctime 1230764762	crtime 1230764777	mtime 1230764762	atime 1230764777	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 34	metaFlags [Allocated, Used, Compressed]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /Compressed/	size 554121	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Compressed/NISTSP800-88_rev1.pdf	fileHandle 0]	File [	]	md5=5b6d3eefd6ac52d4deb81e8d6db6265b
AbstractContent [	objId 0000000025	name NIST_logo.jpg	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Compressed/NIST_logo.jpg	getParent 19]	AbstractFile [		fileType FS	ctime 1230763607	crtime 1230764606	mtime 1230763607	atime 1231191700	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 31	metaFlags [Allocated, Used, Compressed]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /Compressed/	size 2205	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Compressed/NIST_logo.jpg	fileHandle 0]	File [	]	md5=3d69f5e5712591755407ab8309654c0e
AbstractContent [	objId 0000000026	name report02-3.pdf	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Compressed/report02-3.pdf	getParent 19]	AbstractFile [		fileType FS	ctime 1230764913	crtime 1230764971	mtime 1230764913	atime 1230764971	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 37	metaFlags [Allocated, Used, Compressed]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /Compressed/	size 1421998	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Compressed/report02-3.pdf	fileHandle 0]	File [	]	md5=dc9a30e5e3985e2f85cf29caf54826ca
AbstractContent [	objId 0000000027	name EFS-key-info.txt	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/EFS-key-info.txt	getParent 3]	AbstractFile [		fileType FS	ctime 1231191948	crtime 1231191947	mtime 1231191948	atime 1231271053	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 46	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 57	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/EFS-key-info.txt	fileHandle 0]	File [	]	md5=d1cca6ecd35530faae146ed0f10205a1
AbstractContent [	objId 0000000028	name EFS-key-no-password.pfx	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/EFS-key-no-password.pfx	getParent 3]	AbstractFile [		fileType FS	ctime 1231191849	crtime 1231191846	mtime 1231191849	atime 1231191876	attrId 4	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 43	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 1730	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/EFS-key-no-password.pfx	fileHandle 0]	File [	]	md5=39d127f9a8eca23e926520feb1c626d5
AbstractContent [	objId 0000000029	name EFS-key-password-strong-protection.pfx	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/EFS-key-password-strong-protection.pfx	getParent 3]	AbstractFile [		fileType FS	ctime 1231191912	crtime 1231191910	mtime 1231191912	atime 1231191912	attrId 4	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 45	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 1734	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/EFS-key-password-strong-protection.pfx	fileHandle 0]	File [	]	md5=aca9f1c7f9a10438e0d9935d6b203f73
AbstractContent [	objId 0000000030	name EFS-key-password.pfx	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/EFS-key-password.pfx	getParent 3]	AbstractFile [		fileType FS	ctime 1231191884	crtime 1231191882	mtime 1231191884	atime 1231191884	attrId 4	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 44	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 1730	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/EFS-key-password.pfx	fileHandle 0]	File [	]	md5=151b13f4ddc9eef8b933899c827e6e3c
AbstractContent [	objId 0000000031	name Encrypted	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Encrypted	getParent 3]	AbstractFile [		fileType FS	ctime 1231192820	crtime 1230763483	mtime 1231192820	atime 1231192820	attrId 10	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 0	gid 0	metaAddr 29	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 56	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Encrypted	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000032	name .	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Encrypted/.	getParent 31]	AbstractFile [		fileType FS	ctime 1231192820	crtime 1230763483	mtime 1231192820	atime 1231192820	attrId 10	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 0	gid 0	metaAddr 29	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /Encrypted/	size 56	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Encrypted/.	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000033	name ..	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Encrypted/..	getParent 31]	AbstractFile [		fileType FS	ctime 1231191948	crtime 1230763442	mtime 1231191948	atime 1231271053	attrId 6	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 48	gid 0	metaAddr 5	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /Encrypted/	size 56	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Encrypted/..	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000034	name 20076517123273.pdf	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Encrypted/20076517123273.pdf	getParent 31]	AbstractFile [		fileType FS	ctime 1230765336	crtime 1230765386	mtime 1230765330	atime 1230765386	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 41	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /Encrypted/	size 1001647	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Encrypted/20076517123273.pdf	fileHandle 0]	File [	]	md5=305bedc9b117d3620c7ad32ba64c0c71
AbstractContent [	objId 0000000035	name logfile1.txt	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Encrypted/logfile1.txt	getParent 31]	AbstractFile [		fileType FS	ctime 1231192886	crtime 1231192820	mtime 1231192886	atime 1231192886	attrId 7	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 49	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /Encrypted/	size 21888890	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Encrypted/logfile1.txt	fileHandle 0]	File [	]	md5=4d51bfced8c66d1eca032f7abf19264e
AbstractContent [	objId 0000000036	name NISTSP800-88_rev1.pdf	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Encrypted/NISTSP800-88_rev1.pdf	getParent 31]	AbstractFile [		fileType FS	ctime 1230764762	crtime 1230764781	mtime 1230764762	atime 1230764781	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 35	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /Encrypted/	size 554121	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Encrypted/NISTSP800-88_rev1.pdf	fileHandle 0]	File [	]	md5=4e9d2b53ef2924c70a4f2c8fab5dd1d1
AbstractContent [	objId 0000000037	name NIST_logo.jpg	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Encrypted/NIST_logo.jpg	getParent 31]	AbstractFile [		fileType FS	ctime 1230763607	crtime 1230764612	mtime 1230763607	atime 1230765416	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 32	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /Encrypted/	size 2205	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Encrypted/NIST_logo.jpg	fileHandle 0]	File [	]	md5=3c732ce60c9391841c1ffcb7e8f8b19c
AbstractContent [	objId 0000000038	name report02-3.pdf	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/Encrypted/report02-3.pdf	getParent 31]	AbstractFile [		fileType FS	ctime 1230764913	crtime 1230764975	mtime 1230764913	atime 1230764975	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 38	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /Encrypted/	size 1421998	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/Encrypted/report02-3.pdf	fileHandle 0]	File [	]	md5=38b1c7a9922e793172e252f71e1f0e36
AbstractContent [	objId 0000000039	name RAW	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/RAW	getParent 3]	AbstractFile [		fileType FS	ctime 1231192820	crtime 1230763457	mtime 1231192820	atime 1231192820	attrId 7	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 0	gid 0	metaAddr 27	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 56	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/RAW	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000040	name .	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/RAW/.	getParent 39]	AbstractFile [		fileType FS	ctime 1231192820	crtime 1230763457	mtime 1231192820	atime 1231192820	attrId 7	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 0	gid 0	metaAddr 27	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /RAW/	size 56	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/RAW/.	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000041	name ..	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/RAW/..	getParent 39]	AbstractFile [		fileType FS	ctime 1231191948	crtime 1230763442	mtime 1231191948	atime 1231271053	attrId 6	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 48	gid 0	metaAddr 5	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /RAW/	size 56	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/RAW/..	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000042	name 20076517123273.pdf	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/RAW/20076517123273.pdf	getParent 39]	AbstractFile [		fileType FS	ctime 1230765336	crtime 1230765389	mtime 1230765330	atime 1230765389	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 42	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /RAW/	size 1001647	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/RAW/20076517123273.pdf	fileHandle 0]	File [	]	md5=bca818f322a5765e0c8a9c8b15cbe288
AbstractContent [	objId 0000000043	name logfile1.txt	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/RAW/logfile1.txt	getParent 39]	AbstractFile [		fileType FS	ctime 1231192883	crtime 1231192820	mtime 1231192883	atime 1231192883	attrId 5	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 47	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /RAW/	size 21888890	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/RAW/logfile1.txt	fileHandle 0]	File [	]	md5=66597ccbb89d7b030e6a2e848117e864
AbstractContent [	objId 0000000044	name NISTSP800-88_rev1.pdf	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/RAW/NISTSP800-88_rev1.pdf	getParent 39]	AbstractFile [		fileType FS	ctime 1230764762	crtime 1230764784	mtime 1230764762	atime 1230764784	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 36	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /RAW/	size 554121	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/RAW/NISTSP800-88_rev1.pdf	fileHandle 0]	File [	]	md5=5b6d3eefd6ac52d4deb81e8d6db6265b
AbstractContent [	objId 0000000045	name NIST_logo.jpg	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/RAW/NIST_logo.jpg	getParent 39]	AbstractFile [		fileType FS	ctime 1230763607	crtime 1230764616	mtime 1230763607	atime 1230764987	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 33	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /RAW/	size 2205	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/RAW/NIST_logo.jpg	fileHandle 0]	File [	]	md5=3d69f5e5712591755407ab8309654c0e
AbstractContent [	objId 0000000046	name report02-3.pdf	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/RAW/report02-3.pdf	getParent 39]	AbstractFile [		fileType FS	ctime 1230764913	crtime 1230764978	mtime 1230764913	atime 1230764978	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_DATA	dirFlag Allocated	dirType REG	uid 0	gid 0	metaAddr 39	metaFlags [Allocated, Used]	metaType r	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IWUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IWGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IWOTH, TSK_FS_META_MODE_IXOTH]	parentPath /RAW/	size 1421998	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/RAW/report02-3.pdf	fileHandle 0]	File [	]	md5=dc9a30e5e3985e2f85cf29caf54826ca
AbstractContent [	objId 0000000047	name System Volume Information	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/System Volume Information	getParent 3]	AbstractFile [		fileType FS	ctime 1230763515	crtime 1230763515	mtime 1230763515	atime 1230763515	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 0	gid 0	metaAddr 30	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /	size 48	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/System Volume Information	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000048	name .	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/System Volume Information/.	getParent 47]	AbstractFile [		fileType FS	ctime 1230763515	crtime 1230763515	mtime 1230763515	atime 1230763515	attrId 1	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 0	gid 0	metaAddr 30	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /System Volume Information/	size 48	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/System Volume Information/.	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000049	name ..	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/System Volume Information/..	getParent 47]	AbstractFile [		fileType FS	ctime 1231191948	crtime 1230763442	mtime 1231191948	atime 1231271053	attrId 6	attrType TSK_FS_ATTR_TYPE_NTFS_IDXROOT	dirFlag Allocated	dirType DIR	uid 48	gid 0	metaAddr 5	metaFlags [Allocated, Used]	metaType d	modes [TSK_FS_META_MODE_IRUSR, TSK_FS_META_MODE_IXUSR, TSK_FS_META_MODE_IRGRP, TSK_FS_META_MODE_IXGRP, TSK_FS_META_MODE_IROTH, TSK_FS_META_MODE_IXOTH]	parentPath /System Volume Information/	size 56	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/System Volume Information/..	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000050	name $OrphanFiles	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01/$OrphanFiles	getParent 3]	AbstractFile [		fileType FS	ctime 0	crtime 0	mtime 0	atime 0	attrId 0	attrType TSK_FS_ATTR_TYPE_NOT_FOUND	dirFlag Allocated	dirType DIR	uid 0	gid 0	metaAddr 96	metaFlags [Allocated, Used]	metaType d	modes []	parentPath /	size 0	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	FsContent [	fsObjId 2	uniquePath /img_ntfs1-gen2.E01/$OrphanFiles	fileHandle 0]	Directory [	]	
AbstractContent [	objId 0000000051	name $Unalloc	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01//$Unalloc	getParent 3]	AbstractFile [		fileType VIRTUAL_DIR	ctime 0	crtime 0	mtime 0	atime 0	attrId 0	attrType TSK_FS_ATTR_TYPE_DEFAULT	dirFlag Allocated	dirType DIR	uid 0	gid 0	metaAddr 0	metaFlags [Allocated, Used]	metaType d	modes []	parentPath /	size 0	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	VirtualDirectory [	]	
AbstractContent [	objId 0000000052	name Unalloc_51_48640_516554240	checkedHasChildren false	hasChildren false	childrenCount -1	getUniquePath /img_ntfs1-gen2.E01//$Unalloc/Unalloc_51_48640_516554240	getParent 51]	AbstractFile [		fileType UNALLOC_BLOCKS	ctime 0	crtime 0	mtime 0	atime 0	attrId 0	attrType TSK_FS_ATTR_TYPE_DEFAULT	dirFlag Unallocated	dirType REG	uid 0	gid 0	metaAddr 0	metaFlags [Unallocated]	metaType r	modes []	parentPath 	size 454411264	knownState UKNOWN	md5Hash null	localPathSet false	localPath null	localAbsPath null	localFile null]	LayoutFile [	]	
