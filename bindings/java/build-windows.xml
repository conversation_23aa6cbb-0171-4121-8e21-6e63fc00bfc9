<?xml version="1.0" encoding="windows-1252"?>
<project name="TSKTestTargets">

	<property name="dlls" value="../../win32/x64/Release"/>
	<property environment="env"/>

	<target name="test"
			description="Runs the regression tests."
			depends="compile-test" >
		<junit fork="on" haltonfailure="yes" dir=".">
			<env key="path" value="${env.Path};${dlls}"/>
			<sysproperty key="rslt" value="${test-results}"/>
			<sysproperty key="gold" value="${test-standards}"/>
			<sysproperty key="inpt" value="${test-input}"/>
			<classpath refid="libraries" />
			<formatter type="plain" usefile="false" />
			<test name="org.sleuthkit.datamodel.timeline.TimelineTestSuite" />
			<test name="org.sleuthkit.datamodel.DataModelTestSuite" />
		</junit>
	</target>

	<target name="test-rebuild"
			description="Rebuilds gold standards for tests."
			depends="compile-test" >
		<java classname="org.sleuthkit.datamodel.DataModelTestSuite" classpathref="libraries"	fork="true" failonerror="true">
			<sysproperty key="java.library.path" value="${dlls}"/>
			<sysproperty key="gold" value="${test-standards}"/>
			<sysproperty key="inpt" value="${test-input}"/>
			<sysproperty key="types" value="${test-types}"/>
		</java>
	</target>

	<target name="check-native-build" depends="check-build-32,check-build-64"/>

	<target name="check-build-32" if="win32.TskLib.exists">
		<uptodate property="native-up-to-date" srcfile="${basedir}/../../win32/Release/libtsk_jni.dll"
				  targetfile="${x86}/win/libtsk_jni.dll"/>
	</target>

	<target name="check-build-64" if="win64.TskLib.exists">
		<uptodate property="native-up-to-date" srcfile="${basedir}/../../win32/x64/Release/libtsk_jni.dll"
				  targetfile="${amd64}/win/libtsk_jni.dll"/>
	</target>

	<target name="copyLibs" description="Copy native libs to the correct folder">
		<property name="tsk.config" value="Release"/>
		<antcall target="copyWinTskLibsToBuildSQLite" />
	</target>

	<target name="copyLibs-Debug" description="Copy native libs to the correct folder">
		<property name="tsk.config" value="Debug"/>
		<antcall target="copyWinTskLibsToBuildSQLite" />
	</target>

	<target name="copyWinTskLibsToBuildSQLite" depends="copyWinTskLibs64ToBuildSQLite, copyWinTskLibs32ToBuild-SQLite" description="Copy Windows DLLs to the correct location, SQLite build." />

	<target name="checkTskLibDirsSQLite">
		<available property="win64.TskLib.exists" type="file" file="${basedir}/../../win32/x64/${tsk.config}/libtsk_jni.dll" />
		<available property="win32.TskLib.exists" type="file" file="${basedir}/../../win32/${tsk.config}/libtsk_jni.dll" />
	</target>

	<target name="copyWinTskLibs64ToBuildSQLite" depends="checkTskLibDirsSQLite" if="win64.TskLib.exists">
		<property name="tsk.jni.64" location="${basedir}/../../win32/x64/${tsk.config}/libtsk_jni.dll" />

		<copy file="${tsk.jni.64}" todir="${amd64}/win" overwrite="true"/>
		<copy file="${tsk.jni.64}" todir="${x86_64}/win" overwrite="true"/>
	</target>

	<target name="copyWinTskLibs32ToBuild-SQLite" depends="checkTskLibDirs" if="win32.TskLib.exists">
		<property name="tsk.jni.32" location="${basedir}/../../win32/${tsk.config}/libtsk_jni.dll" />

		<copy file="${tsk.jni.32}" todir="${i386}/win" overwrite="true"/>
		<copy file="${tsk.jni.32}" todir="${x86}/win" overwrite="true"/>
		<copy file="${tsk.jni.32}" todir="${i586}/win" overwrite="true"/>
		<copy file="${tsk.jni.32}" todir="${i686}/win" overwrite="true"/>
	</target>

	<target name="checkTskLibDirs">
		<available property="win64.TskLib.exists" type="file" file="${basedir}/../../win32/x64/${tsk.config}/libtsk_jni.dll" />
		<available property="win32.TskLib.exists" type="file" file="${basedir}/../../win32/${tsk.config}/libtsk_jni.dll" />
	</target>

</project>
