/*! \mainpage The Sleuth Kit Java Bindings Developer's Guide and API Reference

<h3>Overview</h3>

These classes allow Java programs to access data extracted by The Sleuth Kit.

The Sleuth Kit is primarily a C/C++ library and set of command line tools. These classes allow programs to obtain the data that TSK can produce.   The typical steps would be to use JNI to cause the TSK library to create and populate a SQLite or PostgreSQL database.  The Java classes then directly open the database and perform queries on it. 


\section main_classes Types of Classes

There are three broad types of classes in this package:
- org.sleuthkit.datamodel.SleuthkitCase contains all of the code that deals with the backend database. 
- org.sleuthkit.datamodel.SleuthkitJNI deals with the JNI bindings with the C/C++ code (that primarily populate the database or allow file content to be read)
- Lots of classes that store information about specific files or volumes.  Nearly all of them implement the org.sleuthkit.datamodel.Content interface.   Files from file systems or carved files will extend org.sleuthkit.datamodel.AbstractFile. 


\section main_workflow Basic Workflow 

\subsection basics_add Adding Data to Case 

To get data into the database (which is needed before you get it into a Java object), you need to call some org.sleuthkit.datamodel.SleuthkitCase methods. 

To open or create a case, call org.sleuthkit.datamodel.SleuthkitCase.newCase() or org.sleuthkit.datamodel.SleuthkitCase.openCase(). 

To add a <b>disk image</b> to the case, use org.sleuthkit.datamodel.SleuthkitCase.makeAddImageProcess() to get a org.sleuthkit.datamodel.SleuthkitJNI.CaseDbHandle.AddImageProcess object that allows you to populate the database in the scope of a transaction and get feedback on its update process. 

To add a <b>local file</b> (logical file) you can use methods such as org.sleuthkit.datamodel.SleuthkitCase.addLocalFile(). 


\subsection basics_analyzing Analyzing Data in Case

You can either access files directly using methods such as org.sleuthkit.datamodel.SleuthkitCase.findFiles() or org.sleuthkit.datamodel.SleuthkitCase.getAbstractFileById(). 

You can also access the data in its tree form by starting with org.sleuthkit.datamodel.SleuthkitCase.getImages() and then calling getChildren() on each of the returned objects.  See the section below on basics of the datamodel structure. 


\section main_other Other Topics

- \subpage mod_dspage describes data source organization 
- \subpage mod_os_accounts_page
- \subpage mod_bbpage is where analysis modules (such as those in Autopsy) can post and save their results. 
- The \subpage artifact_catalog_page gives a list of the current artifacts and attributes used on \ref mod_bbpage.
- \subpage mod_compage is where analysis modules can store and retrieve communications-related data. 

\section main_db Database Topics
The Sleuth Kit has its own database schema that is shared with Autopsy and other tools. The primary way it gets populated is via the Java code. 

- Database Schema Documentation:
 - \subpage db_schema_9_4_page 
 - \subpage db_schema_page "Older schemas"
- Refer to \subpage query_database_page if you are going to use one of the SleuthkitCase methods that requires you to specify a query. 
- Refer to \subpage insert_and_update_database_page if you are a Sleuth Kit developer and want to avoid database issues.


*/


