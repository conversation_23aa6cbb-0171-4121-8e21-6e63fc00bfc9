<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\tools\logicalimager\json.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\LogicalImagerDateRule.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\LogicalImagerExtensionRule.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\LogicalImagerFilenameRule.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\LogicalImagerPathRule.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\LogicalImagerRuleBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\LogicalImagerRuleSet.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\LogicalImagerSizeRule.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\TskFindFiles.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\TskHelper.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\RegistryAnalyzer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\RegHiveType.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\RegParser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\RegKey.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\RegVal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\RegistryLoader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\RegFileInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\UserAccount.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\LogicalImagerConfiguration.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\Version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\DriveUtil.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\ReportUtil.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\FileExtractor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tools\logicalimager\MatchedRuleInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\tools\logicalimager\LogicalImagerDateRule.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\LogicalImagerExtensionRule.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\LogicalImagerFilenameRule.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\LogicalImagerPathRule.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\LogicalImagerRuleBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\LogicalImagerRuleSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\LogicalImagerSizeRule.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\tsk_logical_imager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\TskFindFiles.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\TskHelper.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\RegistryAnalyzer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\RegParser.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\RegKey.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\RegVal.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\RegistryLoader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\RegFileInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\UserAccount.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\LogicalImagerConfiguration.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\DriveUtil.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\ReportUtil.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\FileExtractor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tools\logicalimager\MatchedRuleInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
</Project>