<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{76EFC06C-1F64-4478-ABE8-79832716B394}</ProjectGuid>
    <RootNamespace>libtsk_core</RootNamespace>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  
  <!-- Debug Configuration -->
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  
  <!-- Release Configuration -->
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  
  <!-- Debug Settings -->
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NOMINMAX;_CRT_SECURE_NO_DEPRECATE;GUID_WINDOWS;_CRT_SECURE_NO_WARNINGS;WIN32;_DEBUG;_LIB;WINVER=0x0601;TSK_CORE_ONLY;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4200;4814;5030</DisableSpecificWarnings>
    </ClCompile>
  </ItemDefinitionGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NOMINMAX;_CRT_SECURE_NO_DEPRECATE;GUID_WINDOWS;_CRT_SECURE_NO_WARNINGS;WIN32;_DEBUG;_LIB;WINVER=0x0601;TSK_CORE_ONLY;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4200;4814;5030</DisableSpecificWarnings>
    </ClCompile>
  </ItemDefinitionGroup>
  
  <!-- Release Settings -->
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NOMINMAX;_CRT_SECURE_NO_DEPRECATE;GUID_WINDOWS;_CRT_SECURE_NO_WARNINGS;WIN32;NDEBUG;_LIB;WINVER=0x0601;TSK_CORE_ONLY;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4200;4814;5030</DisableSpecificWarnings>
    </ClCompile>
  </ItemDefinitionGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>$(ProjectDir)\..\..\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NOMINMAX;_CRT_SECURE_NO_DEPRECATE;GUID_WINDOWS;_CRT_SECURE_NO_WARNINGS;WIN32;NDEBUG;_LIB;WINVER=0x0601;TSK_CORE_ONLY;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4200;4814;5030</DisableSpecificWarnings>
    </ClCompile>
  </ItemDefinitionGroup>
  
  <!-- Core Module Source Files -->
  <ItemGroup>
    <!-- Base Module -->
    <ClCompile Include="..\..\tsk\base\md5c.c" />
    <ClCompile Include="..\..\tsk\base\mymalloc.c" />
    <ClCompile Include="..\..\tsk\base\sha1c.c" />
    <ClCompile Include="..\..\tsk\base\tsk_endian.c" />
    <ClCompile Include="..\..\tsk\base\tsk_error.c" />
    <ClCompile Include="..\..\tsk\base\tsk_error_win32.cpp" />
    <ClCompile Include="..\..\tsk\base\tsk_list.c" />
    <ClCompile Include="..\..\tsk\base\tsk_lock.c" />
    <ClCompile Include="..\..\tsk\base\tsk_parse.c" />
    <ClCompile Include="..\..\tsk\base\tsk_printf.c" />
    <ClCompile Include="..\..\tsk\base\tsk_stack.c" />
    <ClCompile Include="..\..\tsk\base\tsk_unicode.c" />
    <ClCompile Include="..\..\tsk\base\tsk_version.c" />
    <ClCompile Include="..\..\tsk\base\XGetopt.c" />
    
    <!-- VS Module -->
    <ClCompile Include="..\..\tsk\vs\bsd.c" />
    <ClCompile Include="..\..\tsk\vs\dos.c" />
    <ClCompile Include="..\..\tsk\vs\gpt.c" />
    <ClCompile Include="..\..\tsk\vs\mac.c" />
    <ClCompile Include="..\..\tsk\vs\mm_io.c" />
    <ClCompile Include="..\..\tsk\vs\mm_open.c" />
    <ClCompile Include="..\..\tsk\vs\mm_part.c" />
    <ClCompile Include="..\..\tsk\vs\mm_types.c" />
    <ClCompile Include="..\..\tsk\vs\sun.c" />
    
    <!-- IMG Module -->
    <ClCompile Include="..\..\tsk\img\img_io.c" />
    <ClCompile Include="..\..\tsk\img\img_open.cpp" />
    <ClCompile Include="..\..\tsk\img\img_types.c" />
    <ClCompile Include="..\..\tsk\img\mult_files.c" />
    <ClCompile Include="..\..\tsk\img\raw.c" />
    <ClCompile Include="..\..\tsk\img\logical_img.c" />
    <ClCompile Include="..\..\tsk\img\unsupported_types.c" />
  </ItemGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
</Project>
