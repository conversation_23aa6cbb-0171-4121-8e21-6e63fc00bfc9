<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\rejistry++\src\RegistryHive.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\REGFHeader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\RegistryKey.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\RegistryByteBuffer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\BinaryBlock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\RegistryValue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\ValueData.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\RegistryHiveFile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\Cell.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\HBIN.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\Record.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\NKRecord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\VKRecord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\LFRecord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\SubkeyListRecord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\DirectSubkeyListRecord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\LHRecord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\DBIndirectRecord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\DBRecord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\ByteBuffer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\Buffer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\Rejistry.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\RejistryException.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\ValueListRecord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\RIRecord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\LIRecord.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\EmptySubkeyList.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\src\RegistryHiveBuffer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\rejistry++\include\librejistry++.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\rejistry++\src\RegistryHiveFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\REGFHeader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\RegistryKey.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\RegistryByteBuffer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\RegistryValue.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\ValueData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\Cell.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\HBIN.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\Record.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\NKRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\VKRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\SubkeyListRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\DirectSubkeyListRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\LFRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\LHRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\DBIndirectRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\DBRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\Buffer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\ByteBuffer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\BinaryBlock.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\RejistryException.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\ValueListRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\RIRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\LIRecord.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\rejistry++\src\RegistryHiveBuffer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
</Project>