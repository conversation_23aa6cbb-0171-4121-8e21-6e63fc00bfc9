<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="vs">
      <UniqueIdentifier>{ea463a38-dc16-4410-8333-48b75a1916e0}</UniqueIdentifier>
    </Filter>
    <Filter Include="fs">
      <UniqueIdentifier>{ded26c4b-698a-47d7-a44e-fe2cdfb0b3c1}</UniqueIdentifier>
    </Filter>
    <Filter Include="auto">
      <UniqueIdentifier>{29b01bdc-51f0-402a-afcd-5598446ee469}</UniqueIdentifier>
    </Filter>
    <Filter Include="base">
      <UniqueIdentifier>{3f962b73-5e48-4022-b993-f7da3364b7e5}</UniqueIdentifier>
    </Filter>
    <Filter Include="hash">
      <UniqueIdentifier>{9837d797-a638-4895-91a9-dc09de38e8cf}</UniqueIdentifier>
    </Filter>
    <Filter Include="img">
      <UniqueIdentifier>{36ca1943-034a-4915-a0ac-ede5f6e26bfd}</UniqueIdentifier>
    </Filter>
    <Filter Include="pool">
      <UniqueIdentifier>{7cbe036c-983e-47eb-b83e-499ecc668ffd}</UniqueIdentifier>
    </Filter>
    <Filter Include="util">
      <UniqueIdentifier>{5ae960da-f555-42cd-bc00-eba8c536e79b}</UniqueIdentifier>
    </Filter>
    <Filter Include="util\Bitlocker">
      <UniqueIdentifier>{8ac041d0-9f31-48c5-aff5-909554fdfe98}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\tsk\vs\bsd.c">
      <Filter>vs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\vs\dos.c">
      <Filter>vs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\vs\gpt.c">
      <Filter>vs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\vs\mac.c">
      <Filter>vs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\vs\mm_io.c">
      <Filter>vs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\vs\mm_open.c">
      <Filter>vs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\vs\mm_part.c">
      <Filter>vs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\vs\mm_types.c">
      <Filter>vs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\vs\sun.c">
      <Filter>vs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\dcalc_lib.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\dcat_lib.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\dls_lib.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\dstat_lib.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\ext2fs.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\ext2fs_dent.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\ext2fs_journal.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fatfs.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fatfs_meta.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\ffind_lib.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\ffs.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\ffs_dent.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fls_lib.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fs_attr.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fs_attrlist.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fs_block.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fs_dir.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fs_file.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fs_inode.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fs_io.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fs_load.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fs_name.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fs_open.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fs_parse.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fs_types.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\hfs.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\hfs_dent.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\hfs_journal.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\hfs_unicompare.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\icat_lib.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\ifind_lib.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\ils_lib.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\iso9660.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\iso9660_dent.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\nofs_misc.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\rawfs.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\swapfs.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\unix_misc.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\walk_cpp.cpp">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\auto\auto.cpp">
      <Filter>auto</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\auto\auto_db.cpp">
      <Filter>auto</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\auto\case_db.cpp">
      <Filter>auto</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\auto\db_sqlite.cpp">
      <Filter>auto</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\auto\sqlite3.c">
      <Filter>auto</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\base\md5c.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\base\mymalloc.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\base\sha1c.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\base\tsk_endian.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\base\tsk_error.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\base\tsk_error_win32.cpp">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\base\tsk_list.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\base\tsk_lock.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\base\tsk_parse.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\base\tsk_printf.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\base\tsk_stack.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\base\tsk_unicode.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\base\tsk_version.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\base\XGetopt.c">
      <Filter>base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\img\aff.c">
      <Filter>img</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\img\img_io.c">
      <Filter>img</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\img\img_types.c">
      <Filter>img</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\img\mult_files.c">
      <Filter>img</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\img\raw.c">
      <Filter>img</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\img\logical_img.c">
      <Filter>img</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fatfs_dent.cpp">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\ntfs.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\ntfs_dent.cpp">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\hashdb\hashkeeper.c">
      <Filter>hash</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\hashdb\idxonly.c">
      <Filter>hash</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\hashdb\md5sum.c">
      <Filter>hash</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\hashdb\nsrl.c">
      <Filter>hash</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\hashdb\hdb_base.c">
      <Filter>hash</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\hashdb\sqlite_hdb.cpp">
      <Filter>hash</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\hashdb\tsk_hashdb.c">
      <Filter>hash</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\hashdb\encase.c">
      <Filter>hash</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\exfatfs.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\yaffs.cpp">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\logical_fs.cpp">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\exfatfs_dent.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\exfatfs_meta.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fatfs_utils.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fatxxfs.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fatxxfs_dent.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\fatxxfs_meta.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\hashdb\binsrch_index.cpp">
      <Filter>hash</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\auto\tsk_db.cpp">
      <Filter>auto</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\img\vmdk.c">
      <Filter>img</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\img\vhd.c">
      <Filter>img</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\auto\guid.cpp">
      <Filter>auto</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\auto\is_image_supported.cpp">
      <Filter>auto</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\img\img_writer.cpp">
      <Filter>img</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\lzvn.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\pool\apfs_pool.cpp">
      <Filter>pool</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\pool\pool_open.cpp">
      <Filter>pool</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\pool\pool_read.cpp">
      <Filter>pool</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\pool\pool_types.cpp">
      <Filter>pool</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\util\crypto.cpp">
      <Filter>util</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\apfs_open.cpp">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\apfs.cpp">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\apfs_compat.cpp">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\apfs_fs.cpp">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\decmpfs.c">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\pool\apfs_pool_compat.cpp">
      <Filter>pool</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\img\ewf.cpp">
      <Filter>img</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\img\img_open.cpp">
      <Filter>img</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\util\detect_encryption.c">
      <Filter>util</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\util\file_system_utils.c">
      <Filter>util</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\img\unsupported_types.c">
      <Filter>img</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\util\Bitlocker\BitlockerParser.cpp">
      <Filter>util\Bitlocker</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\util\Bitlocker\DataTypes.cpp">
      <Filter>util\Bitlocker</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\util\Bitlocker\MetadataEntry.cpp">
      <Filter>util\Bitlocker</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\util\Bitlocker\MetadataUtils.cpp">
      <Filter>util\Bitlocker</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\util\Bitlocker\MetadataValueAesCcmEncryptedKey.cpp">
      <Filter>util\Bitlocker</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\util\Bitlocker\MetadataValueKey.cpp">
      <Filter>util\Bitlocker</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\util\Bitlocker\MetadataValueOffsetAndSize.cpp">
      <Filter>util\Bitlocker</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\util\Bitlocker\MetadataValueStretchKey.cpp">
      <Filter>util\Bitlocker</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\util\Bitlocker\MetadataValueVolumeMasterKey.cpp">
      <Filter>util\Bitlocker</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\fs\encryptionHelper.cpp">
      <Filter>fs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\util\Bitlocker\BitlockerUtils.cpp">
      <Filter>util\Bitlocker</Filter>
    </ClCompile>
    <ClCompile Include="..\..\tsk\util\Bitlocker\MetadataValueUnicode.cpp">
      <Filter>util\Bitlocker</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\tsk\vs\tsk_bsd.h">
      <Filter>vs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\vs\tsk_dos.h">
      <Filter>vs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\vs\tsk_gpt.h">
      <Filter>vs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\vs\tsk_mac.h">
      <Filter>vs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\vs\tsk_sun.h">
      <Filter>vs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\vs\tsk_vs.h">
      <Filter>vs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\vs\tsk_vs_i.h">
      <Filter>vs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\tsk_ext2fs.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\tsk_fatfs.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\tsk_ffs.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\tsk_fs.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\tsk_fs_i.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\tsk_hfs.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\tsk_iso9660.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\tsk_ntfs.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\auto\sqlite3.h">
      <Filter>auto</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\auto\tsk_auto.h">
      <Filter>auto</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\auto\tsk_auto_i.h">
      <Filter>auto</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\auto\tsk_case_db.h">
      <Filter>auto</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\auto\tsk_db_sqlite.h">
      <Filter>auto</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\base\tsk_base.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\base\tsk_base_i.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\base\tsk_os.h">
      <Filter>base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\hashdb\tsk_hashdb.h">
      <Filter>hash</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\hashdb\tsk_hashdb_i.h">
      <Filter>hash</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\img\aff.h">
      <Filter>img</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\img\ewf.h">
      <Filter>img</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\img\raw.h">
      <Filter>img</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\img\logical_img.h">
      <Filter>img</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\img\tsk_img.h">
      <Filter>img</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\img\tsk_img_i.h">
      <Filter>img</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\tsk_exfatfs.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\tsk_fatxxfs.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\tsk_yaffs.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\tsk_logical_fs.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\hashdb\tsk_hash_info.h">
      <Filter>hash</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\auto\tsk_db.h">
      <Filter>auto</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\img\vmdk.h">
      <Filter>img</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\img\vhd.h">
      <Filter>img</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\auto\guid.h">
      <Filter>auto</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\auto\tsk_is_image_supported.h">
      <Filter>auto</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\img\img_writer.h">
      <Filter>img</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\tsk_apfs.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\tsk_apfs.hpp">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\pool\pool_compat.hpp">
      <Filter>pool</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\pool\tsk_apfs.h">
      <Filter>pool</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\pool\tsk_apfs.hpp">
      <Filter>pool</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\pool\tsk_pool.h">
      <Filter>pool</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\pool\tsk_pool.hpp">
      <Filter>pool</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\crypto.hpp">
      <Filter>util</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\lw_shared_ptr.hpp">
      <Filter>util</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\span.hpp">
      <Filter>util</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\libtsk.h" />
    <ClInclude Include="..\..\tsk\fs\apfs_compat.hpp">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\apfs_fs.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\apfs_fs.hpp">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\decmpfs.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\pool\apfs_pool_compat.hpp">
      <Filter>pool</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\img\pool.hpp">
      <Filter>img</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\detect_encryption.h">
      <Filter>util</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\file_system_utils.h">
      <Filter>util</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\img\unsupported_types.h">
      <Filter>img</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\Bitlocker\BitlockerParser.h">
      <Filter>util\Bitlocker</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\Bitlocker\DataTypes.h">
      <Filter>util\Bitlocker</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\Bitlocker\MetadataEntry.h">
      <Filter>util\Bitlocker</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\Bitlocker\MetadataUtils.h">
      <Filter>util\Bitlocker</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\Bitlocker\MetadataValue.h">
      <Filter>util\Bitlocker</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\Bitlocker\MetadataValueAesCcmEncryptedKey.h">
      <Filter>util\Bitlocker</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\Bitlocker\MetadataValueKey.h">
      <Filter>util\Bitlocker</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\Bitlocker\MetadataValueOffsetAndSize.h">
      <Filter>util\Bitlocker</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\Bitlocker\MetadataValueStretchKey.h">
      <Filter>util\Bitlocker</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\Bitlocker\MetadataValueVolumeMasterKey.h">
      <Filter>util\Bitlocker</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\fs\encryptionHelper.h">
      <Filter>fs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\Bitlocker\BitlockerUtils.h">
      <Filter>util\Bitlocker</Filter>
    </ClInclude>
    <ClInclude Include="..\..\tsk\util\Bitlocker\MetadataValueUnicode.h">
      <Filter>util\Bitlocker</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
</Project>