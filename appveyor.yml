version: 4.6.0.{build}

environment:
  matrix:
  - job_name: Windows Build
    appveyor_build_worker_image: Visual Studio 2019
  - job_name: Linux Build
    appveyor_build_worker_image: Ubuntu2204
  - job_name: macOS Build
    appveyor_build_worker_image: macos-sonoma

matrix:

  fast_finish: true


# job-specific configurations
for:

  -
    matrix:
      only:
        - job_name: Windows Build

    cache:
      - C:\Users\<USER>\.ant
      - C:\ProgramData\chocolatey\bin
      - C:\ProgramData\chocolatey\lib

    install:
      - ps: choco install nuget.commandline
      - ps: choco install ant --ignore-dependencies
      - ps: $env:Path="C:\Program Files\Java\jdk1.8.0\bin;$($env:Path);C:\ProgramData\chocolatey\lib\ant"
      - set PATH=C:\Python36-x64\';%PATH%
    environment:
      global:
        TSK_HOME: "%APPVEYOR_BUILD_FOLDER%"
        PYTHON: "C:\\Python36-x64"
        JDK_HOME: C:\Program Files\Java\jdk1.8.0
    services:

    before_build:
      - nuget restore win32\libtsk -PackagesDirectory win32\packages

    build_script:
      - python win32\updateAndBuildAll.py -m
      - ps: ant -version
      - ps: pushd bindings/java
      - cmd: ant -q dist
      - ps: popd
      - ps: pushd case-uco/java
      - cmd: ant -q
      - ps: popd

    test_script:
      - cmd: ant -q -f bindings/java test

  -
    matrix:
      only:
        - job_name: Linux Build

    install:
      - sudo apt update --allow-releaseinfo-change
      - sudo apt install -y autoconf python3-venv

    build_script:
      - cat /etc/os-release
      - ./bootstrap
      - ./configure
      - make -j

    test_script:
      - make -j check VERBOSE=1

  -
    matrix:
      only:
        - job_name: macOS Build

    build_script:
      - ./bootstrap
      - ./configure
      - make -j

    get_test_data:
      - cd ..
      - pwd
      - curl https://digitalcorpora.s3.amazonaws.com/corpora/drives/tsk-2024/sleuthkit_test_data.zip -o sleuthkit_test_data.zip
      - unzip sleuthkit_test_data.zip
      - cd sleuthkit_test_data
      - make unpack
      - find . -ls | grpe -v '[.]git'

    test_script:
      - make -j check VERBOSE=1
