#!/usr/bin/env python3
"""
TSK Core Modules Only Build Script
编译TSK核心模块（base, img, vs, fs）
"""

import os
import sys
import subprocess
import shutil

# 配置
MSBUILD_PATH = "C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/MSBuild/Current/Bin/MSBuild.exe"
TSK_ROOT = os.path.dirname(os.path.abspath(__file__))

def build_core_modules(platform="x64", configuration="Release"):
    """
    编译TSK核心模块
    
    Args:
        platform: Win32 或 x64
        configuration: Debug 或 Release
    """
    print(f"Building TSK Core Modules - {platform} {configuration}")
    
    # 核心模块源文件列表
    core_sources = {
        "base": [
            "tsk/base/md5c.c",
            "tsk/base/mymalloc.c", 
            "tsk/base/sha1c.c",
            "tsk/base/tsk_endian.c",
            "tsk/base/tsk_error.c",
            "tsk/base/tsk_error_win32.cpp",
            "tsk/base/tsk_list.c",
            "tsk/base/tsk_lock.c",
            "tsk/base/tsk_parse.c",
            "tsk/base/tsk_printf.c",
            "tsk/base/tsk_stack.c",
            "tsk/base/tsk_unicode.c",
            "tsk/base/tsk_version.c",
            "tsk/base/XGetopt.c"
        ],
        "vs": [
            "tsk/vs/bsd.c",
            "tsk/vs/dos.c", 
            "tsk/vs/gpt.c",
            "tsk/vs/mac.c",
            "tsk/vs/mm_io.c",
            "tsk/vs/mm_open.c",
            "tsk/vs/mm_part.c",
            "tsk/vs/mm_types.c",
            "tsk/vs/sun.c"
        ],
        "img": [
            "tsk/img/img_io.c",
            "tsk/img/img_open.cpp",
            "tsk/img/img_types.c",
            "tsk/img/mult_files.c",
            "tsk/img/raw.c",
            "tsk/img/logical_img.c",
            "tsk/img/unsupported_types.c"
        ],
        "fs": [
            "tsk/fs/fs_inode.c",
            "tsk/fs/fs_io.c",
            "tsk/fs/fs_block.c",
            "tsk/fs/fs_open.c",
            "tsk/fs/fs_name.c",
            "tsk/fs/fs_dir.c",
            "tsk/fs/fs_types.c",
            "tsk/fs/fs_attr.c",
            "tsk/fs/fs_attrlist.c",
            "tsk/fs/fs_load.c",
            "tsk/fs/fs_parse.c",
            "tsk/fs/fs_file.c",
            "tsk/fs/unix_misc.c",
            "tsk/fs/nofs_misc.c",
            "tsk/fs/ffs.c",
            "tsk/fs/ffs_dent.c",
            "tsk/fs/ext2fs.c",
            "tsk/fs/ext2fs_dent.c",
            "tsk/fs/ext2fs_journal.c",
            "tsk/fs/fatfs.c",
            "tsk/fs/fatfs_meta.c",
            "tsk/fs/fatfs_dent.cpp",
            "tsk/fs/fatxxfs.c",
            "tsk/fs/fatxxfs_meta.c",
            "tsk/fs/fatxxfs_dent.c",
            "tsk/fs/exfatfs.c",
            "tsk/fs/exfatfs_meta.c",
            "tsk/fs/exfatfs_dent.c",
            "tsk/fs/fatfs_utils.c",
            "tsk/fs/ntfs.c",
            "tsk/fs/ntfs_dent.cpp",
            "tsk/fs/swapfs.c",
            "tsk/fs/rawfs.c",
            "tsk/fs/iso9660.c",
            "tsk/fs/iso9660_dent.c",
            "tsk/fs/hfs.c",
            "tsk/fs/hfs_dent.c",
            "tsk/fs/hfs_journal.c",
            "tsk/fs/hfs_unicompare.c",
            "tsk/fs/dcalc_lib.c",
            "tsk/fs/dcat_lib.c",
            "tsk/fs/dls_lib.c",
            "tsk/fs/dstat_lib.c",
            "tsk/fs/ffind_lib.c",
            "tsk/fs/fls_lib.c",
            "tsk/fs/icat_lib.c",
            "tsk/fs/ifind_lib.c",
            "tsk/fs/ils_lib.c",
            "tsk/fs/walk_cpp.cpp",
            "tsk/fs/yaffs.cpp",
            "tsk/fs/logical_fs.cpp"
        ]
    }
    
    # 编译器设置
    compiler_flags = [
        "/DNOMINMAX",
        "/D_CRT_SECURE_NO_DEPRECATE", 
        "/DGUID_WINDOWS",
        "/D_CRT_SECURE_NO_WARNINGS",
        "/DWIN32",
        "/D_LIB",
        "/DWINVER=0x0601",
        "/DTSK_CORE_ONLY",
        f"/I{TSK_ROOT}",
        "/W3"
    ]
    
    if configuration == "Debug":
        compiler_flags.extend(["/D_DEBUG", "/Od", "/Zi", "/MDd"])
    else:
        compiler_flags.extend(["/DNDEBUG", "/O2", "/MT"])
    
    # 创建输出目录
    output_dir = f"win32/{platform}/{configuration}_Core"
    os.makedirs(output_dir, exist_ok=True)
    
    # 编译各模块
    object_files = []
    for module, sources in core_sources.items():
        print(f"Compiling {module} module...")
        for source in sources:
            source_path = os.path.join(TSK_ROOT, source)
            if not os.path.exists(source_path):
                print(f"Warning: Source file not found: {source_path}")
                continue
                
            obj_name = os.path.basename(source).replace('.c', '.obj').replace('.cpp', '.obj')
            obj_path = os.path.join(output_dir, f"{module}_{obj_name}")
            
            # 编译命令
            cmd = ["cl.exe"] + compiler_flags + ["/c", source_path, f"/Fo{obj_path}"]
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, cwd=TSK_ROOT)
                if result.returncode != 0:
                    print(f"Error compiling {source}: {result.stderr}")
                    return False
                object_files.append(obj_path)
            except Exception as e:
                print(f"Failed to compile {source}: {e}")
                return False
    
    # 创建静态库
    lib_path = os.path.join(output_dir, "libtsk_core.lib")
    lib_cmd = ["lib.exe", "/OUT:" + lib_path] + object_files
    
    try:
        result = subprocess.run(lib_cmd, capture_output=True, text=True, cwd=TSK_ROOT)
        if result.returncode != 0:
            print(f"Error creating library: {result.stderr}")
            return False
        print(f"Successfully created: {lib_path}")
        return True
    except Exception as e:
        print(f"Failed to create library: {e}")
        return False

def main():
    if len(sys.argv) < 3:
        print("Usage: python build_core_only.py <platform> <configuration>")
        print("Platform: Win32 or x64")
        print("Configuration: Debug or Release")
        sys.exit(1)
    
    platform = sys.argv[1]
    configuration = sys.argv[2]
    
    if platform not in ["Win32", "x64"]:
        print("Invalid platform. Use Win32 or x64")
        sys.exit(1)
        
    if configuration not in ["Debug", "Release"]:
        print("Invalid configuration. Use Debug or Release")
        sys.exit(1)
    
    success = build_core_modules(platform, configuration)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
