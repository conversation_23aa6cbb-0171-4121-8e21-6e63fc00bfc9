# NetBeans user-specific settings
/bindings/java/nbproject/private/
 
# Bindings dependecies and build folders
/bindings/java/lib/
/bindings/java/build/
/bindings/java/dist
/bindings/java/doxygen/tskjni_doxygen.tag
/bindings/java/test/output/results
/bindings/java/test/output/gold/dummy
/bindings/java/test/output/gold/*_BU.txt
/bindings/java/test/output/gold/*_CPP.txt
/bindings/java/test/output/gold/*_CPP_SRT.txt
/bindings/java/test/input
/bindings/java/nbproject/genfiles.properties
/bindings/java/nbproject/nbjdk.properties
/bindings/java/nbproject/jdk.xml
/bindings/java/nbproject/nbjdk.xml
/bindings/java/libts*
*~
*.class
/bindings/java/build/
/bindings/java/dist/
/bindings/java/nbproject/*
!/bindings/java/nbproject/project.xml
!/bindings/java/nbproject/project.properties

# Nuget packages
/win32/packages/

# CASE-UCO build and release folder
/case-uco/java/build/
/case-uco/java/dist/
/case-uco/java/nbproject/private/
/case-uco/java/nbproject/genfiles.properties

# Windows build folders
/win32/__pycache__/
/win32/Debug_NoLibs/
/win32/*/Debug_NoLibs/
/win32/Debug/
/win32/Debug_PostgreSQL/
/win32/*/Debug/
/win32/*/Debug_PostgreSQL/
/win32/Release/
/win32/Release_PostgreSQL/
/win32/*/Release/
/win32/*/Release_PostgreSQL/
/win32/Release_NoLibs/
/win32/*/Release_NoLibs/
/win32/Release_XPNoLibs/
/win32/*/Release_XPNoLibs/
/win32/*/x64/
/win32/x64/
/win32/*/*.user
win32/ipch
win32/BuildErrors.txt
win32/BuildErrors-64bit.txt
win32/.vs
win32/tsk-win.VC.VC.opendb
win32/tsk-win.VC.opendb
win32/tsk-win.VC.db
framework/msvcpp/framework/Debug/
framework/msvcpp/framework/Release/
framework/msvcpp/*/*.user
framework/msvcpp/*/Debug/
framework/msvcpp/*/Release/
framework/msvcpp/BuildLog.txt
framework/msvcpp/*/ipch
framework/runtime/
framework/SampleConfig/to_install/
framework/modules/*/win32/Debug/
framework/modules/*/win32/Release/
framework/modules/*/win32/*.user
framework/modules/c_InterestingFilesModule/tsk
framework/config.h
framework/tools/tsk_analyzeimg/tsk_analyzeimg
framework/tools/tsk_validatepipeline/tsk_validatepipeline
rejistry++/msvcpp/*/Debug
rejistry++/msvcpp/*/Release
rejistry++/msvcpp/*/Release_NoLibs
rejistry++/msvcpp/*/Release_XPNoLibs
rejistry++/msvcpp/*/x64
rejistry++/msvcpp/*/*.user
rejistry++/msvcpp/rejistry++/ipch

# Release files
release/sleuthkit-*
release/clone

# IntelliSense data
/win32/*.ncb
/win32/*.sdf
framework/msvcpp/framework/*.ncb
framework/msvcpp/framework/*sdf
rejistry++/msvcpp/rejistry++/*.ncb
rejistry++/msvcpp/rejistry++/*sdf

# Visual Studio user options
/win32/tsk-win.suo
framework/msvcpp/framework/*.suo
rejistry++/msvcpp/rejistry++/*suo
*.sln.cache
win32/tsk-win.opensdf

# Make crud
*.o
*.lo
*.la
*.jar
Makefile
.deps
.libs
*.swp

#javadoc generated
/bindings/java/javadoc

# Files generated by running configure
*.in
stamp-h1
tsk/tsk_config.h
tsk/tsk_incs.h
tsk/tsk.pc
aclocal.m4
autom4te.cache
config.log
config.status
configure
libtool
m4/libtool.m4
m4/lt*.m4
config/*


# Executables
samples/callback_cpp_style
samples/callback_style
samples/posix_cpp_style
samples/posix_style
samples/*.exe
tests/*.exe
tests/*.log
tests/*.trs
tests/fs_attrlist_apis
tests/fs_fname_apis
tests/fs_thread_test
tests/read_apis
tools/autotools/tsk_comparedir
tools/autotools/tsk_gettimes
tools/autotools/tsk_imageinfo
tools/autotools/tsk_loaddb
tools/autotools/tsk_recover
tools/fiwalk/plugins/jpeg_extract
tools/fiwalk/src/fiwalk
tools/fiwalk/src/test_arff
tools/fstools/blkcat
tools/fstools/blkcalc
tools/fstools/blkls
tools/fstools/blkstat
tools/fstools/fcat
tools/fstools/ffind
tools/fstools/fls
tools/fstools/fsstat
tools/fstools/icat
tools/fstools/ifind
tools/fstools/ils
tools/fstools/istat
tools/fstools/jcat
tools/fstools/jls
tools/fstools/usnjls
tools/hashtools/hfind
tools/imgtools/img_cat
tools/imgtools/img_stat
tools/pooltools/pstat
tools/sorter/sorter
tools/srchtools/sigfind
tools/srchtools/srch_strings
tools/timeline/mactime
tools/vstools/mmcat
tools/vstools/mmls
tools/vstools/mmstat
tools/*/*.exe
tools/*/*/*.exe
unit_tests/base/*.log
unit_tests/base/*.trs
unit_tests/base/test_base
# EMACS backup files
*~

.dirstamp

# Mac Junk
.DS_Store

# Test images
*.img
*.vhd
*.E01
*.vmdk

sleuthkit-*.tar.gz

#Test data folder

tests/data
