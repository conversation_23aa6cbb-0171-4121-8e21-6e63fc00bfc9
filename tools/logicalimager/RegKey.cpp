/*
** The Sleuth Kit
**
** <PERSON> [carrier <at> sleuthkit [dot] org]
** Copyright (c) 2010-2019 <PERSON>.  All Rights reserved
**
** This software is distributed under the Common Public License 1.0
**
*/

#include <iostream>

#include "RegKey.h"

RegKey::<PERSON><PERSON><PERSON>(std::wstring &keyName) : m_regKey(NULL), m_keyName(keyName) {
    m_numSubkeys = -1;// unknown
    m_numValues = -1; // unknown

    m_modifyTime.dwLowDateTime = 0;
    m_modifyTime.dwHighDateTime = 0;
}

RegKey::Reg<PERSON>ey(std::wstring &keyName, long numSubkeys, long numValues) : 
    m_regKey(NULL), 
    m_keyName(keyName),
    m_numSubkeys(numSubkeys),
    m_numValues(numValues)
{
    m_modifyTime.dwLowDateTime = 0;
    m_modifyTime.dwHighDateTime = 0;
}

RegKey::~<PERSON><PERSON><PERSON>() {
    if (m_regKey != NULL) {
        delete m_regKey;
        m_regKey = NULL;
    }
}

/**
 * Initialize a RegKey object from a Rejistry::RegistryKey object.
 *
 * @param regKey a Rejistry::RegistryKey object
 * @returns 0 if initialization is successful, otherwise -1.
 */
int RegKey::initialize(const Rejistry::RegistryKey *regKey) {
    if (regKey == NULL) {
        return -1;
    }

    m_keyName = regKey->getName();
    // TODO - replace the following 2 lines when these methods are available in PR #1665
    // m_numSubkeys = regKey->getSubkeyListSize();
    // m_numValues = regKey->getValueListSize();
    m_numSubkeys = regKey->getSubkeyList().size();
    m_numValues = regKey->getValueList().size();
    uint64_t timestamp = regKey->getTimestamp();
    m_modifyTime.dwLowDateTime = (DWORD)(timestamp & 0xFFFFFFFF);
    m_modifyTime.dwHighDateTime = (DWORD)(timestamp >> 32);

    m_regKey = new Rejistry::RegistryKey(*regKey);

    return 0;
}

/**
* Print the RegKey
*/
void RegKey::print() {
    std::wcout << L"Key: " << m_keyName << std::endl;
    std::wcout << L"\t" << L"Subkeys: " << m_numSubkeys << std::endl;
    std::wcout << L"\t" << L"Values: " << m_numValues << std::endl;
}
