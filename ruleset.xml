<?xml version="1.0"?>
<ruleset name="Autopsy ruleset"
    xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 http://pmd.sourceforge.net/ruleset_2_0_0.xsd">
  <description>
  Ruleset used by Autopsy
  </description>
  <rule ref="rulesets/java/basic.xml/SimplifiedTernary"/>
  <rule ref="rulesets/java/basic.xml/AvoidUsingOctalValues"/>
  <rule ref="rulesets/java/basic.xml/BigIntegerInstantiation"/>
  <rule ref="rulesets/java/basic.xml/ClassCastExceptionWithToArray"/>
  <rule ref="rulesets/java/basic.xml/ForLoopShouldBeWhileLoop"/>
  <rule ref="rulesets/java/basic.xml/CheckResultSet"/>
  <rule ref="rulesets/java/basic.xml/UnconditionalIfStatement"/>
  <rule ref="rulesets/java/basic.xml/CheckSkipResult"/>
  <rule ref="rulesets/java/basic.xml/DontUseFloatTypeForLoopIndices"/>
  <rule ref="rulesets/java/basic.xml/MisplacedNullCheck"/>
  <rule ref="rulesets/java/basic.xml/AvoidDecimalLiteralsInBigDecimalConstructor"/>
  <rule ref="rulesets/java/basic.xml/BooleanInstantiation"/>
  <rule ref="rulesets/java/basic.xml/ExtendsObject"/>
  <rule ref="rulesets/java/basic.xml/AvoidBranchingStatementAsLastInLoop"/>
  <rule ref="rulesets/java/basic.xml/DoubleCheckedLocking"/>
  <rule ref="rulesets/java/basic.xml/ReturnFromFinallyBlock"/>
  <rule ref="rulesets/java/basic.xml/AvoidThreadGroup"/>
  <rule ref="rulesets/java/basic.xml/CollapsibleIfStatements"/>
  <rule ref="rulesets/java/basic.xml/AvoidUsingHardCodedIP"/>
  <rule ref="rulesets/java/basic.xml/CheckResultSet"/>
  <rule ref="rulesets/java/basic.xml/DontCallThreadRun"/>
  <rule ref="rulesets/java/basic.xml/BrokenNullCheck"/>
  <rule ref="rulesets/java/basic.xml/AvoidMultipleUnaryOperators"/>
  <rule ref="rulesets/java/basic.xml/OverrideBothEqualsAndHashcode"/>
  <rule ref="rulesets/java/basic.xml/JumbledIncrementer"/>
  <rule ref="rulesets/java/braces.xml/WhileLoopsMustUseBraces"/>
  <rule ref="rulesets/java/braces.xml/IfStmtsMustUseBraces"/>
  <rule ref="rulesets/java/braces.xml/IfElseStmtsMustUseBraces"/>
  <rule ref="rulesets/java/braces.xml/ForLoopsMustUseBraces"/>
  <rule ref="rulesets/java/clone.xml/ProperCloneImplementation"/>
  <rule ref="rulesets/java/clone.xml/CloneThrowsCloneNotSupportedException"/>
  <rule ref="rulesets/java/clone.xml/CloneMethodMustImplementCloneable"/>
  <rule ref="rulesets/java/clone.xml/CloneMethodReturnTypeMustMatchClassName"/>
  <rule ref="rulesets/java/clone.xml/CloneMethodMustBePublic"/>
  <rule ref="rulesets/java/codesize.xml/NPathComplexity"/>
  <rule ref="rulesets/java/codesize.xml/ExcessiveMethodLength"/>
  <rule ref="rulesets/java/codesize.xml/ExcessiveParameterList"/>
  <rule ref="rulesets/java/codesize.xml/ExcessiveClassLength"/>
  <rule ref="rulesets/java/codesize.xml/CyclomaticComplexity"/>
  <rule ref="rulesets/java/codesize.xml/StdCyclomaticComplexity"/>
  <rule ref="rulesets/java/codesize.xml/ModifiedCyclomaticComplexity"/>
  <rule ref="rulesets/java/codesize.xml/ExcessivePublicCount"/>
  <rule ref="rulesets/java/codesize.xml/TooManyFields"/>
  <rule ref="rulesets/java/codesize.xml/NcssMethodCount"/>
  <rule ref="rulesets/java/codesize.xml/NcssTypeCount"/>
  <rule ref="rulesets/java/codesize.xml/NcssConstructorCount"/>
  <rule ref="rulesets/java/codesize.xml/TooManyMethods"/>
  <rule ref="rulesets/java/comments.xml/CommentRequired">
    <properties>            
      <!-- Disabled because we have lots of undocumented fields -->
      <property name="fieldCommentRequirement" value="Ignored"/>
      <!-- Disabled because we don't require comments on overrides of public fields -->
      <property name="protectedMethodCommentRequirement" value="Ignored"/>
      <property name="publicMethodCommentRequirement" value="Ignored"/>
      <!--<property name="methodWithOverrideCommentRequirement" value="Unwanted"/>-->
    </properties>
  </rule>
  <!-- Commented out because it was flagged some of our header / copyright comments
  <rule ref="rulesets/java/comments.xml/CommentSize"/> -->
  <rule ref="rulesets/java/comments.xml/CommentContent"/>
  <!-- Commented out because we use default (package) access often and commenting it does not make sense for us
  <rule ref="rulesets/java/comments.xml/CommentDefaultAccessModifier"/> -->
  <!--
    Commented out because they are controversial and we want basics right now.
  <rule ref="rulesets/java/controversial.xml/NullAssignment"/>
  <rule ref="rulesets/java/controversial.xml/OnlyOneReturn"/>
  <rule ref="rulesets/java/controversial.xml/AssignmentInOperand"/>
  <rule ref="rulesets/java/controversial.xml/AtLeastOneConstructor"/>
  <rule ref="rulesets/java/controversial.xml/DontImportSun"/>
  <rule ref="rulesets/java/controversial.xml/SuspiciousOctalEscape"/>
  <rule ref="rulesets/java/controversial.xml/CallSuperInConstructor"/>
  <rule ref="rulesets/java/controversial.xml/UnnecessaryParentheses"/>
  <rule ref="rulesets/java/controversial.xml/DataflowAnomalyAnalysis"/>
  <rule ref="rulesets/java/controversial.xml/AvoidFinalLocalVariable"/>
  <rule ref="rulesets/java/controversial.xml/AvoidUsingShortType"/>
  <rule ref="rulesets/java/controversial.xml/AvoidUsingVolatile"/>
  <rule ref="rulesets/java/controversial.xml/AvoidUsingNativeCode"/>
  <rule ref="rulesets/java/controversial.xml/AvoidAccessibilityAlteration"/>
  <rule ref="rulesets/java/controversial.xml/DoNotCallGarbageCollectionExplicitly"/>
  <rule ref="rulesets/java/controversial.xml/AvoidPrefixingMethodParameters"/>
  <rule ref="rulesets/java/controversial.xml/AvoidLiteralsInIfCondition"/>
  <rule ref="rulesets/java/controversial.xml/UseObjectForClearerAPI"/>
  <rule ref="rulesets/java/controversial.xml/UseConcurrentHashMap"/>
  <rule ref="rulesets/java/controversial.xml/OneDeclarationPerLine"/>
  <rule ref="rulesets/java/controversial.xml/DefaultPackage"/>
  <rule ref="rulesets/java/controversial.xml/UnnecessaryConstructor"/>
  -->
  <rule ref="rulesets/java/coupling.xml/CouplingBetweenObjects"/>
  <!--<rule ref="rulesets/java/coupling.xml/ExcessiveImports"/>-->
  <rule ref="rulesets/java/coupling.xml/LooseCoupling"/>
  <rule ref="rulesets/java/coupling.xml/LoosePackageCoupling"/>
  <!-- Commented out because we have not enforced this in the past
  <rule ref="rulesets/java/coupling.xml/LawOfDemeter"/> -->
  <rule ref="rulesets/java/design.xml/UseUtilityClass"/>
  <rule ref="rulesets/java/design.xml/AvoidDeeplyNestedIfStmts"/>
  <rule ref="rulesets/java/design.xml/SwitchDensity"/>
  <rule ref="rulesets/java/design.xml/ConstructorCallsOverridableMethod"/>
  <!--<rule ref="rulesets/java/design.xml/AccessorClassGeneration"/>-->
  <rule ref="rulesets/java/design.xml/FinalFieldCouldBeStatic"/>
  <rule ref="rulesets/java/design.xml/CloseResource"/>
  <rule ref="rulesets/java/design.xml/NonStaticInitializer"/>
  <rule ref="rulesets/java/design.xml/DefaultLabelNotLastInSwitchStmt"/>
  <rule ref="rulesets/java/design.xml/OptimizableToArrayCall"/>
  <rule ref="rulesets/java/design.xml/BadComparison"/>
  <!-- Disabled.  Could enforce in future, but not top priority right now.
  <rule ref="rulesets/java/design.xml/ConfusingTernary"/>-->
  <rule ref="rulesets/java/design.xml/InstantiationToGetClass"/>
  <rule ref="rulesets/java/design.xml/IdempotentOperations"/>
  <rule ref="rulesets/java/design.xml/SimpleDateFormatNeedsLocale"/>
  <rule ref="rulesets/java/design.xml/ImmutableField"/>
  <!-- Disabled.  Could enforce in future, but not top priority right now.
  <rule ref="rulesets/java/design.xml/UseLocaleWithCaseConversions"/> -->
  <rule ref="rulesets/java/design.xml/AvoidProtectedFieldInFinalClass"/>
  <!-- Disabled.  Could enforce in future, but not top priority right now.
  <rule ref="rulesets/java/design.xml/AvoidSynchronizedAtMethodLevel"/>-->
  <rule ref="rulesets/java/design.xml/UseNotifyAllInsteadOfNotify"/>
  <rule ref="rulesets/java/design.xml/AbstractClassWithoutAbstractMethod"/>
  <rule ref="rulesets/java/design.xml/SimplifyConditional"/>
  <!-- Disabled.  Could enforce in future, but not top priority right now.
  <rule ref="rulesets/java/design.xml/PositionLiteralsFirstInCaseInsensitiveComparisons"/>-->
  <rule ref="rulesets/java/design.xml/UnnecessaryLocalBeforeReturn"/>
  <rule ref="rulesets/java/design.xml/NonThreadSafeSingleton"/>
  <rule ref="rulesets/java/design.xml/SingleMethodSingleton"/>
  <rule ref="rulesets/java/design.xml/SingletonClassReturningNewInstance"/>
  <rule ref="rulesets/java/design.xml/UncommentedEmptyConstructor"/>
  <rule ref="rulesets/java/design.xml/AvoidConstantsInterface"/>
  <rule ref="rulesets/java/design.xml/UnsynchronizedStaticDateFormatter"/>
  <rule ref="rulesets/java/design.xml/PreserveStackTrace"/>
  <rule ref="rulesets/java/design.xml/UseCollectionIsEmpty"/>
  <rule ref="rulesets/java/design.xml/ClassWithOnlyPrivateConstructorsShouldBeFinal"/>
  <rule ref="rulesets/java/design.xml/EmptyMethodInAbstractClassShouldBeAbstract"/>
  <rule ref="rulesets/java/design.xml/ReturnEmptyArrayRatherThanNull"/>
  <rule ref="rulesets/java/design.xml/AbstractClassWithoutAnyMethod"/>
  <rule ref="rulesets/java/design.xml/TooFewBranchesForASwitchStatement"/>
  <rule ref="rulesets/java/design.xml/UseVarargs"/>
  <!-- Commented out because it flagged many of the NetBeans created UI classes that place
    fields at various places in the class
    <rule ref="rulesets/java/design.xml/FieldDeclarationsShouldBeAtStartOfClass"/> -->
  <rule ref="rulesets/java/design.xml/GodClass"/>
  <rule ref="rulesets/java/design.xml/AvoidProtectedMethodInFinalClassNotExtending"/>
  <rule ref="rulesets/java/design.xml/ConstantsInInterface"/>
  <rule ref="rulesets/java/design.xml/CompareObjectsWithEquals"/>
  <rule ref="rulesets/java/design.xml/LogicInversion"/>
  <rule ref="rulesets/java/design.xml/SingularField"/>
  <rule ref="rulesets/java/design.xml/SimplifyBooleanReturns"/>
  <rule ref="rulesets/java/design.xml/AvoidInstanceofChecksInCatchClause"/>
  <rule ref="rulesets/java/design.xml/NonCaseLabelInSwitchStatement"/>
  <rule ref="rulesets/java/design.xml/NonStaticInitializer"/>
  <rule ref="rulesets/java/design.xml/AvoidReassigningParameters"/>
  <rule ref="rulesets/java/design.xml/SwitchStmtsShouldHaveDefault"/>
  <rule ref="rulesets/java/design.xml/MissingBreakInSwitch"/>
  <rule ref="rulesets/java/design.xml/EqualsNull"/>
  <rule ref="rulesets/java/design.xml/UncommentedEmptyMethodBody"/>
  <!--<rule ref="rulesets/java/design.xml/SimplifyBooleanExpressions"/>-->
  <rule ref="rulesets/java/design.xml/AssignmentToNonFinalStatic"/>
  <rule ref="rulesets/java/design.xml/MissingStaticMethodInNonInstantiatableClass"/>
  <!-- Disabled.  Could enforce in future, but not top priority right now.
  <rule ref="rulesets/java/design.xml/PositionLiteralsFirstInComparisons"/>-->
  <rule ref="rulesets/java/empty.xml/EmptyCatchBlock"/>
  <rule ref="rulesets/java/empty.xml/EmptyWhileStmt"/>
  <rule ref="rulesets/java/empty.xml/EmptyFinallyBlock"/>
  <rule ref="rulesets/java/empty.xml/EmptyIfStmt"/>
  <rule ref="rulesets/java/empty.xml/EmptyStatementNotInLoop"/>
  <rule ref="rulesets/java/empty.xml/EmptyTryBlock"/>
  <rule ref="rulesets/java/empty.xml/EmptyStatementBlock"/>
  <rule ref="rulesets/java/empty.xml/EmptyStaticInitializer"/>
  <rule ref="rulesets/java/empty.xml/EmptyInitializer"/>
  <rule ref="rulesets/java/empty.xml/EmptySwitchStatements"/>
  <rule ref="rulesets/java/empty.xml/EmptySynchronizedBlock"/>
  <rule ref="rulesets/java/finalizers.xml/FinalizeOnlyCallsSuperFinalize"/>
  <rule ref="rulesets/java/finalizers.xml/FinalizeOverloaded"/>
  <rule ref="rulesets/java/finalizers.xml/FinalizeDoesNotCallSuperFinalize"/>
  <rule ref="rulesets/java/finalizers.xml/FinalizeShouldBeProtected"/>
  <rule ref="rulesets/java/finalizers.xml/AvoidCallingFinalize"/>
  <rule ref="rulesets/java/finalizers.xml/EmptyFinalizer"/>
  <rule ref="rulesets/java/imports.xml/TooManyStaticImports"/>
  <rule ref="rulesets/java/imports.xml/UnusedImports"/>
  <rule ref="rulesets/java/imports.xml/ImportFromSamePackage"/>
  <rule ref="rulesets/java/imports.xml/DuplicateImports"/>
  <rule ref="rulesets/java/imports.xml/DontImportJavaLang"/>
  <!-- Disabled because flagging use of Bundle for bundle messages
  is not desirable since this qualification helps with code completion
  while inappropriate use of full qualification is otherwise very rare
  <rule ref="rulesets/java/imports.xml/UnnecessaryFullyQualifiedName"/>-->
  <!-- Disabled because it generated a lot of errors for non-Beans
  <rule ref="rulesets/java/javabeans.xml/BeanMembersShouldSerialize"/>-->
  <rule ref="rulesets/java/javabeans.xml/MissingSerialVersionUID"/>
  <rule ref="rulesets/java/logging-java.xml/MoreThanOneLogger"/>
  <rule ref="rulesets/java/logging-java.xml/LoggerIsNotStaticFinal"/>
  <rule ref="rulesets/java/logging-java.xml/SystemPrintln"/>
  <rule ref="rulesets/java/logging-java.xml/AvoidPrintStackTrace"/>
  <!-- Disabled because we don't want If conditions around all Log statements 
  <rule ref="rulesets/java/logging-java.xml/GuardLogStatementJavaUtil"/> -->
  <rule ref="rulesets/java/logging-java.xml/InvalidSlf4jMessageFormat"/>
  <rule ref="rulesets/java/migrating.xml/ReplaceVectorWithList"/>
  <rule ref="rulesets/java/migrating.xml/ReplaceHashtableWithMap"/>
  <rule ref="rulesets/java/migrating.xml/ReplaceEnumerationWithIterator"/>
  <rule ref="rulesets/java/migrating.xml/AvoidEnumAsIdentifier"/>
  <rule ref="rulesets/java/migrating.xml/AvoidAssertAsIdentifier"/>
  <rule ref="rulesets/java/migrating.xml/IntegerInstantiation"/>
  <rule ref="rulesets/java/migrating.xml/ByteInstantiation"/>
  <rule ref="rulesets/java/migrating.xml/ShortInstantiation"/>
  <rule ref="rulesets/java/migrating.xml/LongInstantiation"/>
  <!-- Disabled because it wanted all private static to be caps 
  <rule ref="rulesets/java/naming.xml/VariableNamingConventions"/> -->
  <rule ref="rulesets/java/naming.xml/AvoidDollarSigns"/>
  <!--<rule ref="rulesets/java/naming.xml/AbstractNaming"/>-->
  <rule ref="rulesets/java/naming.xml/SuspiciousHashcodeMethodName"/>
  <rule ref="rulesets/java/naming.xml/SuspiciousConstantFieldName"/>
  <rule ref="rulesets/java/naming.xml/SuspiciousEqualsMethodName"/>
  <rule ref="rulesets/java/naming.xml/AvoidFieldNameMatchingTypeName"/>
  <rule ref="rulesets/java/naming.xml/AvoidFieldNameMatchingMethodName"/>
  <rule ref="rulesets/java/naming.xml/MisleadingVariableName"/>
  <rule ref="rulesets/java/naming.xml/ShortClassName"/>
  <rule ref="rulesets/java/naming.xml/ClassNamingConventions"/>
  <rule ref="rulesets/java/naming.xml/MethodNamingConventions"/>
  <!--<rule ref="rulesets/java/naming.xml/GenericsNaming"/>-->
  <rule ref="rulesets/java/naming.xml/NoPackage"/>
  <rule ref="rulesets/java/naming.xml/MethodWithSameNameAsEnclosingClass"/>
  <!--<rule ref="rulesets/java/naming.xml/ShortVariable"/>-->
  <!-- Commented out because clarity trumps brevity, developers can use their own judgement
  <rule ref="rulesets/java/naming.xml/LongVariable"/>  -->
  <rule ref="rulesets/java/naming.xml/ShortMethodName"/>
  <rule ref="rulesets/java/naming.xml/BooleanGetMethodName"/>
  <rule ref="rulesets/java/naming.xml/PackageCase"/>
  <!-- Commented out because this is a nice micro-refinement, but too much of our code does not do this, developers can, but don't have to ignore this practice for now
  <rule ref="rulesets/java/optimizations.xml/LocalVariableCouldBeFinal"/>
  <rule ref="rulesets/java/optimizations.xml/MethodArgumentCouldBeFinal"/>  -->
  <!-- Commented out because this is not typical coding practice and it seems of dubious value, e.g., https://stackoverflow.com/questions/17340421/pmd-avoid-instantiating-new-objects-inside-loops/17458503
  <rule ref="rulesets/java/optimizations.xml/AvoidInstantiatingObjectsInLoops"/>   -->
  <rule ref="rulesets/java/optimizations.xml/UseArrayListInsteadOfVector"/>
  <rule ref="rulesets/java/optimizations.xml/SimplifyStartsWith"/>
  <!-- Disabled.  Could enforce in future, but not top priority right now.
  <rule ref="rulesets/java/optimizations.xml/UseStringBufferForStringAppends"/>-->
  <rule ref="rulesets/java/optimizations.xml/UseArraysAsList"/>
  <rule ref="rulesets/java/optimizations.xml/AvoidArrayLoops"/>
  <rule ref="rulesets/java/optimizations.xml/UnnecessaryWrapperObjectCreation"/>
  <rule ref="rulesets/java/optimizations.xml/AddEmptyString"/>
  <!--<rule ref="rulesets/java/optimizations.xml/RedundantFieldInitializer"/>-->
  <rule ref="rulesets/java/optimizations.xml/PrematureDeclaration"/>
  <rule ref="rulesets/java/strictexception.xml/SignatureDeclareThrowsException"/>
  <rule ref="rulesets/java/strictexception.xml/AvoidCatchingThrowable"/>
  <rule ref="rulesets/java/strictexception.xml/ExceptionAsFlowControl"/>
  <rule ref="rulesets/java/strictexception.xml/AvoidCatchingNPE"/>
  <rule ref="rulesets/java/strictexception.xml/AvoidRethrowingException"/>
  <rule ref="rulesets/java/strictexception.xml/DoNotExtendJavaLangError"/>
  <rule ref="rulesets/java/strictexception.xml/DoNotThrowExceptionInFinally"/>
  <rule ref="rulesets/java/strictexception.xml/AvoidThrowingNewInstanceOfSameException"/>
  <rule ref="rulesets/java/strictexception.xml/AvoidLosingExceptionInformation"/>
  <rule ref="rulesets/java/strictexception.xml/AvoidCatchingGenericException"/>
  <rule ref="rulesets/java/strictexception.xml/AvoidThrowingRawExceptionTypes"/>
  <rule ref="rulesets/java/strictexception.xml/AvoidThrowingNullPointerException"/>
  <!--<rule ref="rulesets/java/strings.xml/AvoidDuplicateLiterals"/>-->
  <!--<rule ref="rulesets/java/strings.xml/InefficientStringBuffering"/>-->
  <rule ref="rulesets/java/strings.xml/AppendCharacterWithChar"/>
  <rule ref="rulesets/java/strings.xml/ConsecutiveAppendsShouldReuse"/>
  <rule ref="rulesets/java/strings.xml/ConsecutiveLiteralAppends"/>
  <rule ref="rulesets/java/strings.xml/UseIndexOfChar"/>
  <rule ref="rulesets/java/strings.xml/InefficientEmptyStringCheck"/>
  <rule ref="rulesets/java/strings.xml/InsufficientStringBufferDeclaration"/>
  <rule ref="rulesets/java/strings.xml/UselessStringValueOf"/>
  <rule ref="rulesets/java/strings.xml/StringBufferInstantiationWithChar"/>
  <rule ref="rulesets/java/strings.xml/AvoidStringBufferField"/>
  <rule ref="rulesets/java/strings.xml/StringInstantiation"/>
  <rule ref="rulesets/java/strings.xml/UseEqualsToCompareStrings"/>
  <rule ref="rulesets/java/strings.xml/StringToString"/>
  <rule ref="rulesets/java/strings.xml/UseStringBufferLength"/>
  <rule ref="rulesets/java/strings.xml/UnnecessaryCaseChange"/>
  <rule ref="rulesets/java/sunsecure.xml/MethodReturnsInternalArray"/>
  <rule ref="rulesets/java/sunsecure.xml/ArrayIsStoredDirectly"/>
  <rule ref="rulesets/java/typeresolution.xml/LooseCoupling"/>
  <rule ref="rulesets/java/typeresolution.xml/CloneMethodMustImplementCloneable"/>
  <rule ref="rulesets/java/typeresolution.xml/UnusedImports"/>
  <rule ref="rulesets/java/typeresolution.xml/SignatureDeclareThrowsException"/>
  <rule ref="rulesets/java/unnecessary.xml/UnnecessaryConversionTemporary"/>
  <rule ref="rulesets/java/unnecessary.xml/UnnecessaryFinalModifier"/>
  <rule ref="rulesets/java/unnecessary.xml/UselessOverridingMethod"/>
  <rule ref="rulesets/java/unnecessary.xml/UnusedNullCheckInEquals"/>
  <!-- Disabled.  Could enforce in future, but not top priority right now
  <rule ref="rulesets/java/unnecessary.xml/UselessParentheses"/>-->
  <rule ref="rulesets/java/unnecessary.xml/UselessQualifiedThis"/>
  <rule ref="rulesets/java/unnecessary.xml/UselessOperationOnImmutable"/>
  <rule ref="rulesets/java/unnecessary.xml/UnnecessaryReturn"/>
  <!--<rule ref="rulesets/java/unusedcode.xml/UnusedModifier"/>-->
  <rule ref="rulesets/java/unusedcode.xml/UnusedLocalVariable"/>
  <!-- Commented out because it flagged many event listeners and other interfaces.
  <rule ref="rulesets/java/unusedcode.xml/UnusedFormalParameter"/> -->
  <rule ref="rulesets/java/unusedcode.xml/UnusedPrivateField"/>
  <rule ref="rulesets/java/unusedcode.xml/UnusedPrivateMethod"/>

</ruleset>
