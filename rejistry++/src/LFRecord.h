/*
 *
 * The Sleuth Kit
 *
 * Copyright 2013-2015 Basis Technology Corp.
 * Contact: carrier <at> sleuthkit <dot> org
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * This is a C++ port of the Rejistry library developed by <PERSON><PERSON>.
 * See https://github.com/williballenthin/Rejistry for the original Java version.
 */

/**
 * \file LFRecord.h
 *
 */
#ifndef _REJISTRY_LFRECORD_H
#define _REJISTRY_LFRECORD_H

#include <cstdint>
#include <string>

// Local includes
#include "DirectSubkeyListRecord.h"

namespace Rejistry {

    /**
     */
    class LFRecord : public DirectSubkeyListRecord {
    public:
        static const std::string MAGIC;

        LFRecord(RegistryByteBuffer * buf, uint32_t offset);
        
        virtual ~LFRecord() {}
    
    private:

        LFRecord() {};
        LFRecord(const LFRecord &);
        LFRecord& operator=(const LFRecord &);
    };
};

#endif
