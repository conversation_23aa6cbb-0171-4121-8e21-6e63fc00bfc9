name: "Build"

# For docker container comparable to Linux build worker:
#   docker run -it docker.io/library/ubuntu:22.04 bash

on:
  push:
    branches:
      - main
      - develop
      - release-4.14.0
  pull_request:
    branches:
      - main
      - develop
      - release-4.14.0

jobs:
  build:
    timeout-minutes: 30
    name: ${{matrix.prefix}} ${{ matrix.os }} ${{ matrix.arch }} ${{ matrix.linkage }} ${{ matrix.compiler }} ${{ matrix.suffix }}
    runs-on: ${{ matrix.runner }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: "macos"
            arch: "arm64"
            linkage: "shared"
            compiler: "gcc"
            runner: "macos-15"
            configure_opts: "--with-libewf --enable-java"
            make_flags: ""
            codecov: "yes"
            prefix: "CODECOV"
            address_sanitizer: "yes"
            keep_artifacts: "no"
            enable_java: "yes"

          - os: "linux"
            arch: "x86_64"
            linkage: "shared"
            compiler: "gcc"
            runner: "ubuntu-24.04"
            configure_opts: "--with-libewf --with-libqcow --with-libvhdi --with-libvmdk --enable-java"
            make_flags: ""
            codecov: "no"
            prefix:
            address_sanitizer: "yes"
            keep_artifacts: "yes"
            suffix: "(with artifacts)"
            enable_java: "yes"

          - os: "linux"
            arch: "x86_64"
            linkage: "shared"
            compiler: "clang"
            runner: "ubuntu-24.04"
            configure_opts: "--with-libewf --with-libqcow --with-libvhdi --with-libvmdk --enable-java CC=clang CXX=clang++"
            make_flags: ""
            codecov: "no"
            prefix: ""
            address_sanitizer: "yes"
            keep_artifacts: "no"
            enable_java: "yes"

    steps:
      - name: Determine number of cores
        id: cores
        run: |
          if [ ${{ startsWith(matrix.runner, 'macos') }} = true ]; then
            CORES=$(sysctl -n hw.logicalcpu)
          elif [ ${{ matrix.os == 'mingw' }} = true ]; then
            # mingw exhausts the memory if too many jobs run concurrently
            CORES=2
          else
            CORES=$(nproc)
          fi
          echo "cores=$CORES" >>$GITHUB_OUTPUT
          echo "Using $CORES cores"

      - name: Install MacOS packages
        env:
          MATRIX_ENABLE_JAVA: ${{ matrix.enable_java }}
        if: ${{ matrix.os == 'macos' }}
        run: |
          export CPPFLAGS=-I/opt/homebrew/include/
          export LDFLAGS=-L/opt/homebrew/lib/
          brew update
          brew install libtool autoconf automake libtool libewf libmagic afflib wget
          echo MATRIX_ENABLE_JAVA=$MATRIX_ENABLE_JAVA
          if [ ${MATRIX_ENABLE_JAVA}x == "yesx" ]; then
             echo Installing JAVA
             brew install openjdk@17
             echo JAVA_HOME="$(brew --prefix openjdk)" >> $GITHUB_ENV
             echo PATH="$JAVA_HOME/bin:$PATH" >> $GITHUB_ENV
             echo JNI_CPPFLAGS="-I$JAVA_HOME/include -I$JAVA_HOME/include/darwin" >> $GITHUB_ENV
             export CPPFLAGS="-I$JAVA_HOME/include $CPPFLAGS"
          fi
          echo search for aff
          find /opt/homebrew -name 'aff*'
          echo "CPPFLAGS=$CPPFLAGS" >> $GITHUB_ENV
          echo "LDFLAGS=$LDFLAGS" >> $GITHUB_ENV

      - name: Install Linux packages
        if: ${{ matrix.os == 'linux' }}
        run: |
          sudo apt update
          sudo apt install -y ant autoconf automake g++ libssl-dev afflib-tools libewf-dev libqcow-dev libvhdi-dev libvmdk-dev libmagic-dev libtool make pkg-config zlib1g-dev wget

      - name: Install Mingw packages and setup for cross-compiling
        if: ${{ matrix.os == 'mingw' }}
        run: |
          sudo dpkg --add-architecture i386
          sudo apt update
          sudo apt install autoconf automake libtool make pkg-config mingw-w64 mingw-w64-tools libz-mingw-w64-dev wine32 wine64 wget
          sudo update-alternatives --set ${{ matrix.arch }}-w64-mingw32-g++ /usr/bin/${{ matrix.arch }}-w64-mingw32-g++-posix
          # This is not working; also requires mingw-w64-x86_64-libgnurx
          # echo === installing libmagic for mingw ===
          # wget -q https://astron.com/pub/file/file-5.44.tar.gz # Check for the latest version at https://astron.com/pub/file/
          # tar -xzf file-5.44.tar.gz
          # cd file-5.44
          # ./configure -q ${{ matrix.configure_optsnj}} && make V=0 && sudo make install

      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          submodules: recursive
          fetch-depth: ${{ matrix.os == 'mingw' && 1 || 0 }}

      - name: Run bootstrap
        run: |
          echo CPPFLAGS=$CPPFLAGS
          echo LDFLAGS=$LDFLAGS
          ./bootstrap

      - name: Run configure
        run: |
          ./configure ${{ matrix.configure_opts }}

      - name: Unpack and List the test data
        run: |
          cd ..
          pwd
          wget https://digitalcorpora.s3.amazonaws.com/corpora/drives/tsk-2024/sleuthkit_test_data.zip
          unzip sleuthkit_test_data.zip
          cd sleuthkit_test_data
          make unpack
          find . -ls | grep -v '[.]git'
          echo "SLEUTHKIT_TEST_DATA_DIR=$(pwd)" >> $GITHUB_ENV

      - name: Run make
        run: |
          make -j${{ steps.cores.outputs.cores }} check ${{ matrix.make_flags }} VERBOSE=1 TESTS=

      - name: run make check
        run: |
          make check

      - name: run the tsk_loaddb test
        run: |
          test/tools/autotools/test_loaddb.sh


#      - name: Run make check on Mac/Linux
#        if: ${{ matrix.os != 'mingw' }}
#        run: |
#          echo SLEUTHKIT_TEST_DATA_DIR=$SLEUTHKIT_TEST_DATA_DIR
#          make -j${{ steps.cores.outputs.cores }} check ${{ matrix.make_flags }} VERBOSE=1 || result=1 ; for i in $(find test -name '*.log') ; do printf '\n%79s\n' | tr ' ' '=' ; echo "$i" ; cat "$i" ; done ; exit $result

#      - name: Run make check on Mingw
#        if: ${{ matrix.os == 'mingw' }}
#        env:
#          WINEARCH: ${{ matrix.winearch }}
#          WINEPATH: ${{ matrix.winepath }}
#          WINEPREFIX: ${{ matrix.wineprefix }}
#        run: |
#          make -j${{ steps.cores.outputs.cores }} check ${{ matrix.make_flags }} VERBOSE=1 LOG_COMPILER=scripts/wine_wrapper.sh || result=1 ; for i in $(find test -name '*.log') ; do printf '\n%79s\n' | tr ' ' '=' ; echo "$i" ; cat "$i" ; done ; exit $result

#      - name: Clean up
#        if: ${{ matrix.os != 'mingw' }}
#        run: |
#          make distclean

#      - name: Run configure with address-sanitizer
#        if: ${{ matrix.address_sanitizer == 'yes' }}
#        run: |
#          ./configure ${{ matrix.configure_opts }} --enable-address-sanitizer CFLAGS=-g CXXFLAGS=-g

#      - name: Check with address-sanitizer
#        if: ${{ matrix.address_sanitizer == 'yes' }}
#        run: |
#          make -j check VERBOSE=1

#      - name: Clean up
#        if: ${{ matrix.os != 'mingw' }}
#        run: |
#          make distclean
#
      - name: Run configure for codecov and remake
        if: ${{ matrix.codecov == 'yes' }}
        run: |
          ./configure ${{ matrix.configure_opts }} CFLAGS='-g -O0 -fprofile-arcs -ftest-coverage' CXXFLAGS='-g -O0 -fprofile-arcs -ftest-coverage'
          V=0 make clean
          V=0 make -j${{ steps.cores.outputs.cores }}

      - name: Run test_loaddb.sh
        run: |
          test/tools/autotools/test_loaddb.sh

#          make -j check V=0
#          #make -j test/fiwalk/fiwalk_test V=0
#          #make -j test/runner V=0
#
#      - name: Run unit tests for codecov
#        if: ${{ matrix.codecov == 'yes' }}
#        run: |
#          test/fiwalk/fiwalk_test -s
#          test/runner -s
#
      - name: run gcov
        if: ${{ matrix.codecov == 'yes' }}
        run: |
          gcov $(find . -name '*.gcda')

      - name: Upload codecov report
        if: ${{ matrix.codecov == 'yes' }}
        uses: codecov/codecov-action@v5
        with:
          fail_ci_if_error: false
          token: ${{ secrets.CODECOV_TOKEN }}
#
#      - name: Create any artifacts that we need to keep
#        if: ${{ matrix.keep_artifacts == 'yes' }}
#        run: |
#          ./configure ${{ matrix.configure_opts }} CFLAGS='-O2' CXXFLAGS='-O2'
#          make tools/fiwalk/src/fiwalk
#          mkdir executables
#          mv tools/fiwalk/src/.libs/fiwalk executables
#
#      - name: Keep artifacts
#        if: ${{ matrix.keep_artifacts == 'yes' }}
#        uses: actions/upload-artifact@v4
#        with:
#          name: executables
#          path: |
#            executables/*
#          retention-days: 15

#      - name: Run distcheck
#        run: |
#          ./configure
#          make distcheck
#
#      - uses: ammaraskar/gcc-problem-matcher@0.2.0
#        name: GCC Problem Matcher
#
